<template>
  <section class="tutors">
    <div class="tutors-decoration--before">
      <v-img
        :src="require('~/assets/images/homepage/decoration-1.svg')"
        contain
        :options="{ rootMargin: '50%' }"
      ></v-img>
    </div>
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.tutors_section_title') }}
            </h3>
            <div class="section-head-subtitle text--gradient">
              {{ $t('home_page.tutors_section_subtitle') }}
            </div>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <v-container class="py-0" fluid>
      <v-row>
        <v-col class="col-12">
          <div class="tutors-carousel">
            <client-only>
              <VueSlickCarousel v-bind="tutorsCarouselSettings">
                <div
                  v-for="(t, idx) in teachers"
                  :key="idx"
                  ref="tutorsCarouselItem"
                  class="tutors-carousel-item"
                >
                  <div class="tutors-carousel-item-left">
                    <div>
                      <nuxt-link :to="t.profileLink">
                        <div class="tutors-carousel-item-image">
                          <v-avatar width="163" height="163">
                            <v-img
                              :src="
                                getSrcAvatar(
                                  t.avatarsResized,
                                  'user_thumb_163x163'
                                )
                              "
                              :srcset="
                                getSrcSetAvatar(
                                  t.avatarsResized,
                                  'user_thumb_163x163',
                                  'user_thumb_326x326'
                                )
                              "
                              :options="{ rootMargin: '50%' }"
                            ></v-img>
                          </v-avatar>
                          <div v-if="t.languagesTaught.length" class="flags">
                            <div
                              v-for="languageTaught in t.languagesTaught"
                              :key="languageTaught.isoCode"
                              class="flags-item"
                            >
                              <v-img
                                :src="
                                  require(`~/assets/images/flags/${languageTaught.isoCode}.svg`)
                                "
                                width="52"
                                height="36"
                                contain
                                :options="{ rootMargin: '50%' }"
                              ></v-img>
                            </div>
                          </div>
                        </div>
                      </nuxt-link>
                      <div>
                        <div class="tutors-carousel-item-name text-uppercase">
                          <nuxt-link :to="t.profileLink">
                            {{ t.firstName }}
                          </nuxt-link>
                        </div>
                        <div class="tutors-carousel-item-rating">
                          <div>
                            <v-img
                              :src="
                                require('~/assets/images/homepage/stars.svg')
                              "
                              width="68"
                              contain
                              :options="{ rootMargin: '50%' }"
                            ></v-img>
                          </div>
                          <span>({{ $tc('review', t.countFeedbacks) }})</span>
                        </div>
                      </div>
                    </div>
                    <ul
                      v-if="t.languagesTaught.length"
                      class="tutors-carousel-item-list"
                    >
                      <li v-for="(lt, index) in t.languagesTaught" :key="index">
                        {{ lt.name }} {{ $t('teacher') }}
                      </li>
                    </ul>
                  </div>
                  <div class="tutors-carousel-item-right">
                    <div class="tutors-carousel-item-text">
                      “{{ t.description }}”
                      <template v-if="t.specialities && t.specialities.length">
                        <span>{{ $t('top_specialities') }}:</span>
                        <div class="tutors-carousel-item-languages">
                          {{ getSpecialitiesStr(t.specialities) }}
                        </div>
                      </template>
                    </div>
                    <div
                      v-if="$vuetify.breakpoint.mdAndUp"
                      class="tutors-carousel-item-button text-center d-none d-md-block"
                    >
                      <v-btn :to="t.profileLink" large color="greyDark">
                        <template v-if="t.bookLesson.freeTrial">
                          {{ $t('free_trial') }}
                        </template>
                        <template v-else>
                          {{ $t('trial') }}:&#160; {{ currentCurrencySymbol
                          }}{{ getPrice(t.bookLesson.price) }}
                        </template>
                      </v-btn>
                    </div>
                  </div>
                  <div
                    v-if="$vuetify.breakpoint.smAndDown"
                    class="tutors-carousel-item-button text-center d-md-none"
                  >
                    <v-btn :to="t.profileLink" large color="greyDark">
                      <template v-if="t.bookLesson.freeTrial">
                        {{ $t('free_trial') }}
                      </template>
                      <template v-else>
                        {{ $t('trial') }}:&#160; {{ currentCurrencySymbol
                        }}{{ getPrice(t.bookLesson.price) }}
                      </template>
                    </v-btn>
                  </div>
                </div>
              </VueSlickCarousel>
            </client-only>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="tutors-button">
            <v-btn
              :to="
                localePath({
                  name: 'teachers',
                  params: {
                    utm_source: 'homepage',
                    utm_medium: 'teacher-cards',
                  },
                })
              "
              large
              color="primary"
            >
              {{ $t('meet_all_our_teachers') }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <div class="tutors-decoration--after">
      <v-img
        :src="require('~/assets/images/homepage/decoration-2.svg')"
        contain
        :options="{ rootMargin: '50%' }"
      ></v-img>
    </div>
  </section>
</template>

<script>
import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'

import Avatars from '~/mixins/Avatars'
import { getPrice } from '~/helpers'

export default {
  name: 'TutorsSection',
  components: {
    VueSlickCarousel,
  },

  mixins: [Avatars],
  props: {
    teachers: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      getPrice,
      tutorsCarouselSettings: {
        dots: false,
        focusOnSelect: true,
        infinite: true,
        speed: 800,
        slidesToShow: 2,
        slidesToScroll: 1,
        responsive: [
          {
            breakpoint: 992,
            settings: {
              slidesToShow: 1,
            },
          },
        ],
      },
    }
  },
  computed: {
    currentCurrencySymbol() {
      return this.$store.getters['currency/currentCurrencySymbol']
    },
  },
  methods: {
    getSpecialitiesStr(arr) {
      if (!arr || !Array.isArray(arr)) return ''

      let str = ''
      for (let i = 0; i < 3; i++) {
        if (arr[i]?.speciality) {
          str += this.getTranslatedSpecialityName(arr[i].speciality)
        }

        if (i < 2 && arr[i + 1]?.speciality) {
          str += ', '
        }
      }
      return str
    },
    getTranslatedSpecialityName(speciality) {
      if (
        !speciality ||
        !speciality.translations ||
        !this.$i18n ||
        !this.$i18n.locale
      ) {
        return speciality?.name || ''
      }
      const currentLocale = this.$i18n.locale
      const translation = speciality.translations.find(
        (t) => t.locale === currentLocale && t.field === 'name'
      )
      return translation ? translation.content : speciality.name
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.tutors {
  position: relative;
  padding: 220px 0 132px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 150px 0 110px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding: 150px 0 80px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding: 90px 0 80px;
  }

  &-decoration--before,
  &-decoration--after {
    position: absolute;
    width: 100%;
  }

  &-decoration--before {
    top: 0;
    left: 0;
    width: 100%;
    max-width: 874px;
    height: 1015px;
    max-height: 1085px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      top: 120px;
      max-width: 690px;
      height: 820px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      top: 55px;
      width: calc(100% - 45px);
      max-width: 480px;
      height: 600px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      top: 0;
      height: 400px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      height: 400px;
    }

    .v-image__image {
      background-position: left center;
    }
  }

  &-decoration--after {
    right: 0;
    top: 145px;
    max-width: 437px;
    height: 100%;
    max-height: 679px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      top: 185px;
      max-width: 305px;
      max-height: 479px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      top: 240px;
      max-width: 190px;
      max-height: 295px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      top: auto;
      bottom: 50px;
    }

    .v-image__image {
      background-position: right center;
    }
  }

  &-carousel {
    position: relative;
    margin-top: 96px;
    z-index: 2;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      margin-top: 50px;
    }

    @media #{map-get($display-breakpoints, 'lg-and-up')} {
      margin-left: -48px;
      max-width: 1262px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      width: calc(100% + 20px);
      padding-bottom: 100px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: calc(100% + 15px);
    }

    .slick-slide {
      padding: 0 37px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        padding: 0 20px 0 0;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 0 15px 0 0;

        & > div {
          display: flex;
          justify-content: center;
        }
      }
    }

    &-item {
      display: inline-flex !important;
      color: #fff;
      background: linear-gradient(
          126.15deg,
          var(--v-green-base) 0%,
          var(--v-primary-base) 102.93%
        ),
        #c4c4c4;
      border-radius: 24px;

      @media #{map-get($display-breakpoints, 'lg-and-up')} {
        max-width: 594px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        position: relative;
        flex-direction: column;
        max-width: 640px;
      }

      &-left {
        width: 238px;
        padding: 12px 32px 18px;
        border-right: 1px solid rgba(255, 255, 255, 0.1);

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          width: 215px;
          padding: 20px 24px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 100%;
          padding: 20px 24px 6px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          border-right: none;

          & > div {
            display: flex;
            justify-content: space-between;
          }
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding: 20px 15px 6px;
        }
      }

      &-right {
        position: relative;
        width: calc(100% - 238px);
        padding: 32px 32px 82px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          padding: 20px 24px 80px;
        }

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          width: calc(100% - 215px);
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 100%;
        }

        @media only screen and (max-width: $xxs-and-down) {
          padding: 32px 15px 95px;
        }
      }

      &-image {
        position: relative;
        width: 163px;
        height: 163px;
        margin: 0 auto 4px;

        @media #{map-get($display-breakpoints, 'md-only')} {
          width: 140px;
          height: 140px;

          .v-avatar {
            width: 140px !important;
            height: 140px !important;
          }
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          margin: 0 38px 0 0;
        }

        @media only screen and (max-width: $xxs-and-down) {
          width: 90px;
          height: 90px;
          margin: 0 25px 0 0;

          .v-avatar {
            width: 90px !important;
            height: 90px !important;
          }
        }

        .flags {
          position: absolute;
          bottom: 5px;
          right: -24px;

          @media only screen and (max-width: $xxs-and-down) {
            width: 38px;
            bottom: 0;
            right: -20px;
          }

          &-item {
            margin-top: 2px;
            border-radius: 8px;
            overflow: hidden;
          }
        }
      }

      &-name {
        font-size: 24px;
        font-weight: 700;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          text-align: right;
        }

        @media only screen and (max-width: $xsm-and-down) {
          font-size: 18px;
        }

        a {
          color: #fff !important;
          text-decoration: none;
        }
      }

      &-rating {
        display: flex;
        align-items: center;
        margin-top: 8px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          flex-direction: column;
          align-items: flex-end;
        }

        span {
          display: inline-block;
          margin-left: 8px;
          font-size: 13px;
          font-weight: 700;
          line-height: 0.8;
          color: var(--v-orange-base);

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            display: block;
            margin: 10px 0 0;
          }
        }
      }

      &-list {
        margin-top: 12px;
        padding-left: 0 !important;
        list-style-type: none;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          display: flex;
          flex-wrap: wrap;
          margin-top: 18px;
          margin-left: -20px;
        }

        & > li {
          position: relative;
          margin-top: 2px;
          padding-left: 19px;
          font-size: 18px;
          line-height: 1.55;

          @media #{map-get($display-breakpoints, 'md-only')} {
            margin-top: 4px;
            font-size: 16px;
            line-height: 1.2;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            margin: 0 0 10px 24px;
          }

          &::before {
            content: '';
            position: absolute;
            top: 7px;
            left: 0;
            width: 11px;
            height: 9px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center center;
            background-image: url('~assets/images/check.svg');
          }
        }
      }

      &-text {
        font-size: 18px;
        line-height: 1.33;

        span {
          display: block;
          margin: 25px 0 8px;
          font-weight: 400;
        }
      }

      &-languages {
        font-weight: 300;
      }

      &-button {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 20px;

        @media only screen and (max-width: $xxs-and-down) {
          padding: 0 24px;
        }

        .v-btn {
          min-width: 222px !important;

          @media #{map-get($display-breakpoints, 'md-and-down')} {
            min-width: 180px !important;
          }

          @media only screen and (max-width: $xxs-and-down) {
            min-width: 100% !important;
            width: 100% !important;
          }
        }
      }
    }
  }

  .slick-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      top: auto;
      bottom: -100px;
      transform: translateX(-50%);
    }
  }

  .slick-prev {
    left: calc(100vw - (100vw - 1412px) / 2 - 80px);

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      left: auto;
      right: -82px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      left: calc(50% - 65px);
      right: auto;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      left: calc(50% - 60px);
    }
  }

  .slick-next {
    left: calc(100vw - (100vw - 1412px) / 2);

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      left: auto;
      right: -162px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      left: calc(50% + 35px);
      right: auto;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      left: calc(50% + 40px);
    }
  }

  &-button {
    display: flex;
    justify-content: center;
    margin-top: 90px;

    @media #{map-get($display-breakpoints, 'lg-and-down')} {
      margin-top: 80px;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-top: 50px;
    }

    .v-btn {
      min-width: 285px !important;

      @media only screen and (max-width: $xxs-and-down) {
        min-width: 100% !important;
        width: 100% !important;
      }
    }
  }
}
</style>
