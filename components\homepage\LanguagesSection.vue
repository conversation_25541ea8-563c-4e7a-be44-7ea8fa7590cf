<template>
  <section class="languages">
    <v-container class="py-0">
      <v-row>
        <v-col class="col-12 py-0">
          <div class="section-head section-head--decorated">
            <h3
              class="section-head-title"
              style="color: #262626; -webkit-text-fill-color: #262626"
            >
              {{ $t('home_page.languages_section_title') }}
            </h3>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <div class="languages-content">
      <div class="languages-content-image">
        <v-img
          :src="require('~/assets/images/homepage/earth-with-arrows.svg')"
          contain
          class="d-none d-sm-block"
          :options="{ rootMargin: '50%' }"
        ></v-img>
        <v-img
          :src="require('~/assets/images/homepage/earth-with-arrows-m.svg')"
          contain
          class="d-sm-none"
          :options="{ rootMargin: '50%' }"
        ></v-img>
        <div class="languages-lines-mobile d-sm-none">
          <div class="languages-line languages-line-r1">
            <div class="languages-country languages-country-pl">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/pl-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,32">
                {{ $t('polish') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-uk">
              <div
                class="languages-country-flag languages-country-flag--double"
              >
                <v-img
                  :src="
                    require('~/assets/images/homepage/flags/uk-us-flag.svg')
                  "
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,12">
                <span v-html="$t('uk_usa_english')"></span>
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-fr">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/fr-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,16">
                {{ $t('french') }}
              </nuxt-link>
            </div>
          </div>
          <div class="languages-line languages-line-r2">
            <div class="languages-country languages-country-it">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/it-flag-2.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,23">
                {{ $t('italian') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-ar">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/ar-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,2">
                {{ $t('arabic') }}
              </nuxt-link>
            </div>
          </div>
          <div class="languages-line languages-line-r3">
            <div class="languages-country languages-country-ge">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/de-flag-2.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,17">
                {{ $t('germany') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-pr-br">
              <div
                class="languages-country-flag languages-country-flag--double"
              >
                <v-img
                  :src="
                    require('~/assets/images/homepage/flags/pr-br-flag.svg')
                  "
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,34">
                <span v-html="$t('portuguese_brazilian')"></span>
              </nuxt-link>
            </div>
          </div>
          <div class="languages-line languages-line-r4">
            <div class="languages-country languages-country-ru">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/ru-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,38">
                {{ $t('russian') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-jp">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/jp-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,24">
                {{ $t('japanese') }}
              </nuxt-link>
            </div>
          </div>
          <div class="languages-line languages-line-r5">
            <div class="languages-country languages-country-sp">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/sp-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,44">
                {{ $t('spanish') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-ch">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/ch-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,7">
                {{ $t('chinese') }}
              </nuxt-link>
            </div>
          </div>
          <div class="languages-line languages-line-r6">
            <div class="languages-country languages-country-du">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/du-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,11">
                {{ $t('dutch') }}
              </nuxt-link>
            </div>
            <div class="languages-country languages-country-sw">
              <div class="languages-country-flag">
                <v-img
                  :src="require('~/assets/images/homepage/flags/sw-flag.svg')"
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </div>
              <nuxt-link to="/teachers/language,45">
                {{ $t('swedish') }}
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
      <v-container class="py-0 d-none d-sm-block">
        <v-row>
          <v-col class="col-xl-10 offset-xl-1 py-0">
            <v-row>
              <v-col class="col-12">
                <div class="languages-line languages-line-r1">
                  <div class="languages-country languages-country-pl">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/pl-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,32">
                      {{ $t('polish') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-uk">
                    <div
                      class="languages-country-flag languages-country-flag--double"
                    >
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/uk-us-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,12">
                      <span v-html="$t('uk_usa_english')"></span>
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-fr">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/fr-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,16">
                      {{ $t('french') }}
                    </nuxt-link>
                  </div>
                </div>
                <div class="languages-line languages-line-r2">
                  <div class="languages-country languages-country-it">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/it-flag-2.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,23">
                      {{ $t('italian') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-ar">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/ar-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,2">
                      {{ $t('arabic') }}
                    </nuxt-link>
                  </div>
                </div>
                <div class="languages-line languages-line-r3">
                  <div class="languages-country languages-country-ge">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/de-flag-2.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,17">
                      {{ $t('germany') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-pr-br">
                    <div
                      class="languages-country-flag languages-country-flag--double"
                    >
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/pr-br-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,34">
                      <span v-html="$t('portuguese_brazilian')"></span>
                    </nuxt-link>
                  </div>
                </div>
                <div class="languages-line languages-line-r4">
                  <div class="languages-country languages-country-ru">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/ru-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,38">
                      {{ $t('russian') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-jp">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/jp-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,24">
                      {{ $t('japanese') }}
                    </nuxt-link>
                  </div>
                </div>
                <div class="languages-line languages-line-r5">
                  <div class="languages-country languages-country-sp">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/sp-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,44">
                      {{ $t('spanish') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-ch">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/ch-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,7">
                      {{ $t('chinese') }}
                    </nuxt-link>
                  </div>
                </div>
                <div class="languages-line languages-line-r6">
                  <div class="languages-country languages-country-du">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/du-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,11">
                      {{ $t('dutch') }}
                    </nuxt-link>
                  </div>
                  <div class="languages-country languages-country-sw">
                    <div class="languages-country-flag">
                      <v-img
                        :src="
                          require('~/assets/images/homepage/flags/sw-flag.svg')
                        "
                        :options="{ rootMargin: '50%' }"
                      ></v-img>
                    </div>
                    <nuxt-link to="/teachers/language,45">
                      {{ $t('swedish') }}
                    </nuxt-link>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </section>
</template>

<script>
export default {
  name: 'LanguagesSection',
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.home-page .languages {
  padding: 90px 0 210px;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding-bottom: 155px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-bottom: 120px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding: 30px 0;
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding: 0 0 30px;
  }

  &-content {
    position: relative;
    margin-top: 136px;

    @media #{map-get($display-breakpoints, 'md-and-down')} {
      margin-top: 96px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-top: 75px;
    }

    @media #{map-get($display-breakpoints, 'xs-only')} {
      padding-left: 28%;
    }

    @media only screen and (max-width: $xxs-and-down) {
      margin-top: 50px;
    }

    &-image {
      position: absolute;
      left: 50%;
      max-width: 932px;
      transform: translateX(-50%);

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        max-width: 760px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        max-width: 550px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        position: relative;
        width: 100%;
        height: 0;
        left: auto;
        right: 0;
        padding-bottom: 168%;
        transform: none;

        & > .v-image {
          position: absolute;
          top: 0;
          right: 0;
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &-lines-mobile {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  &-line {
    display: flex;
    justify-content: center;
    width: 100%;

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      position: relative;
    }

    &-r1 {
      & > div {
        padding: 0 45px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
          padding: 0 28px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding: 0 14px;
        }

        @media #{map-get($display-breakpoints, 'xs-only')} {
          padding: 0;
        }
      }
    }
  }

  &-country {
    position: relative;
    display: flex;
    align-items: center;
    line-height: 1.3;

    @media #{map-get($display-breakpoints, 'xs-only')} {
      position: absolute;
      margin: 0 !important;
    }

    &-flag {
      display: flex;
      align-items: center;
      width: 32px;
      margin-right: 16px;

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: 24px;
        margin-right: 12px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        margin-right: 6px;
      }

      &--double {
        width: 49px;

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          width: 39px;
        }
      }
    }

    a {
      font-size: 24px;
      font-weight: 600;
      color: var(--v-darkLight-base) !important;
      text-decoration: none;
      transition: color 0.2s;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        font-size: 18px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 16px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        white-space: nowrap;
      }

      &:hover {
        color: var(--v-orange-base) !important;
      }
    }

    &-uk {
      top: -28px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        top: -22px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        top: auto;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 84%;
        right: 60%;
        left: auto;
      }

      @media only screen and (max-width: $xxs-and-down) {
        right: 65%;
      }
    }

    &-fr {
      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 73%;
        right: 79%;
        left: auto;
      }
    }

    &-pl {
      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 100%;
        right: 88%;
        left: auto;
      }
    }

    &-it {
      margin-top: 80px;
      left: -322px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 55px;
        left: -266px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 46px;
        left: -192px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 43%;
        right: 69%;
        left: auto;
      }
    }

    &-ar {
      margin-top: 50px;
      left: 408px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 16px;
        left: 318px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 12px;
        left: 238px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 62%;
        right: 68%;
        left: auto;
      }
    }

    &-ge {
      margin-top: 90px;
      left: -382px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 68px;
        left: -260px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 42px;
        left: -190px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 12%;
        left: auto;
        right: 38%;
      }

      @media only screen and (max-width: $xxs-and-down) {
        bottom: 10%;
      }
    }

    &-pr-br {
      margin-top: 70px;
      left: 435px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 50px;
        left: 308px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 28px;
        left: 245px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 17%;
        right: 68%;
        left: auto;
      }
    }

    &-ru {
      margin-top: 106px;
      left: -444px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 55px;
        left: -320px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 55px;
        left: -260px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 18%;
        left: auto;
        right: 6%;
      }
    }

    &-jp {
      margin-top: 95px;
      left: 466px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 66px;
        left: 352px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 40px;
        left: 268px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 31%;
        right: 82%;
        left: auto;
      }
    }

    &-sp {
      margin-top: 140px;
      left: -415px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 135px;
        left: -330px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 80px;
        left: -245px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 52%;
        right: 85%;
        left: auto;
      }
    }

    &-ch {
      margin-top: 15px;
      left: 415px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 12px;
        left: 324px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 7px;
        left: 248px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 95%;
        right: 46%;
        left: auto;
      }
    }

    &-du {
      margin-top: 60px;
      left: -260px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 28px;
        left: -206px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 22px;
        left: -145px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        bottom: 28%;
        right: 40%;
        left: auto;
      }

      @media only screen and (max-width: $xxs-and-down) {
        bottom: 26%;
        right: 28%;
      }
    }

    &-sw {
      margin-top: 54px;
      left: 290px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        margin-top: 42px;
        left: 235px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 32px;
        left: 170px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        top: -3%;
        right: 12%;
        left: auto;
      }

      @media only screen and (max-width: $xxs-and-down) {
        top: -6%;
        right: 2%;
      }
    }
  }
}

.es.home-page {
  @media #{map-get($display-breakpoints, 'lg-and-up')} {
    .languages-country-uk {
      top: -50px;
    }

    .languages-country-jp {
      margin-top: 20px;
    }

    .languages-country-pr-br {
      margin-top: 35px;
    }
  }

  @media #{map-get($display-breakpoints, 'md-only')} {
    .languages-country-uk {
      top: -40px;
    }

    .languages-country-ge {
      margin-top: 35px;
    }

    .languages-country-jp {
      margin-top: 50px;
    }
  }

  @media #{map-get($display-breakpoints, 'sm-only')} {
    .languages-country-uk {
      top: -40px;
    }

    .languages-country-ge {
      margin-top: 26px;
    }

    .languages-country-jp {
      margin-top: 35px;
    }

    .languages-country-pr-br {
      margin-top: 12px;
    }
  }

  @media only screen and (max-width: $xxs-and-down) {
    .languages-country-uk {
      right: 46%;
    }
  }
}
</style>
