export default async function ({ redirect, route, store, app }) {
  const ids = [51, 52, 53, 54, 56, 58, 59, 60, 61, 63, 64, 65, 66] // array of hidden specialities

  // Don't redirect if on new URL structure with slugs
  // Check route name or path for slug structure
  const routeName = route.name || ''
  const routePath = route.path || ''

  // If route name indicates we're on the slug-based structure, exit immediately
  if (
    routeName.includes('speciality-language') ||
    routeName.includes('_speciality-_language')
  ) {
    return // Already on new URL structure
  }

  // If path has slugs (contains hyphen and no numeric params), exit
  if (routePath.includes('-') && !/\d+/.test(route.params.params)) {
    return // Already on new URL structure
  }

  // Check if params.params has hyphen and is not the old format
  if (
    route.params.params &&
    route.params.params.includes('-') &&
    !route.params.params.includes(',')
  ) {
    return // Already using new URL structure (slug format)
  }

  // Handle both old path-based params and new query-based params
  let paramsStr = ''
  if (route.params.params) {
    paramsStr = route.params.params
  } else if (route.query.searchString) {
    paramsStr = route.query.searchString
  }

  // Don't run if no params or if query params already exist (new format)
  if (!paramsStr) return

  // Don't run if there are query params already (already converted to new format)
  if (Object.keys(route.query).length > 0 && !route.query.searchString) {
    return
  }

  const paramsObj = {}

  paramsStr.split(';').forEach((item) => {
    let arr = item.split(',')

    const property = arr[0]

    arr.splice(0, 1)
    arr = arr.map((item) => +item)

    paramsObj[property] = arr
  })

  // Handle hidden specialties
  if (
    paramsObj.speciality &&
    Array.isArray(paramsObj.speciality) &&
    ids.some((id) => paramsObj.speciality.includes(id))
  ) {
    if (paramsObj.speciality.length === 1) {
      redirect(301, '/teachers')
      return
    }

    paramsObj.speciality = [...paramsObj.speciality].filter(
      (id) => !ids.includes(id)
    )

    const properties = Object.keys(paramsObj)

    let paramsWithoutHiddenSpecialities = ''

    for (let i = 0; i < properties.length; i++) {
      paramsWithoutHiddenSpecialities +=
        properties[i] + ',' + paramsObj[properties[i]].toString()

      if (i < properties.length - 1) {
        paramsWithoutHiddenSpecialities += ';'
      }
    }

    // Redirect to new query parameter structure
    redirect(
      301,
      `/teachers?searchString=${encodeURIComponent(
        paramsWithoutHiddenSpecialities
      )}`
    )
    return
  }

  // Convert old URL structure to new format
  // Old: /teachers/page/language,X;speciality,Y;...
  // New: /teachers/specialitySlug-languageSlug?...
  if (
    paramsObj.language &&
    paramsObj.speciality &&
    Array.isArray(paramsObj.language) &&
    Array.isArray(paramsObj.speciality) &&
    paramsObj.language.length > 0 &&
    paramsObj.speciality.length > 0
  ) {
    // Get filters to convert IDs to names
    // If filters not loaded yet, fetch them
    let filters = store.state.teacher_filter.filters
    if (!filters) {
      await store.dispatch('teacher_filter/getFilters', {
        skipAutoSelect: true,
      })
      filters = store.state.teacher_filter.filters
    }

    if (filters && filters.languages) {
      // Get all specialities from all motivations
      const allSpecialities =
        filters.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      const language = filters.languages.find(
        (lang) => lang.id === paramsObj.language[0]
      )
      const speciality = allSpecialities.find(
        (spec) => spec.id === paramsObj.speciality[0]
      )

      if (language && speciality) {
        // Convert to URL-friendly names first
        const languageSlug = language.name.toLowerCase().replace(/\s+/g, '-')
        const specialitySlug = speciality.name
          .toLowerCase()
          .replace(/\s+/g, '-')

        // Don't set filters in store here - let the page handle it from query params
        // This prevents infinite redirect loops

        // Build query string from other params (exclude language/speciality as they're in the URL path)
        const queryParams = []

        // Add other params (language and speciality are already in the URL path)
        Object.keys(paramsObj).forEach((key) => {
          if (key !== 'language' && key !== 'speciality') {
            paramsObj[key].forEach((value) => {
              queryParams.push(`${key}=${value}`)
            })
          }
        })

        // Build new URL
        const queryString = `?${queryParams.join('&')}`
        const newUrl = `/teachers/${specialitySlug}-${languageSlug}${queryString}`

        redirect(301, newUrl)
      }
    }
  }
}
