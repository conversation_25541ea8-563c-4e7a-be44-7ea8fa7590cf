<template>
  <user-setting-template :title="$t('basic_info')" :submit-func="submitData">
    <div class="mb-3">
      <v-row>
        <v-col class="col-12 col-md-7 mb-3 mb-sm-2">
          <div class="user-settings-avatar">
            <div
              class="d-flex flex-column flex-sm-row align-center justify-center justify-sm-start"
            >
              <v-avatar
                class="mb-1 mb-sm-0 mr-sm-2"
                size="124px"
                @click="$refs.fileAvatar.$el.querySelector('input').click()"
              >
                <v-img
                  :src="
                    getSrcAvatar(basicInfoItem.avatars, 'user_thumb_124x124')
                  "
                  :srcset="
                    getSrcSetAvatar(
                      basicInfoItem.avatars,
                      'user_thumb_124x124',
                      'user_thumb_248x248'
                    )
                  "
                  :options="{ rootMargin: '50%' }"
                ></v-img>
              </v-avatar>
              <div class="text-center text-sm-left body-2">
                {{ $t('click_photo_to_upload_new_one') }}
              </div>
            </div>

            <div class="input-wrap">
              <v-file-input
                ref="fileAvatar"
                v-model="file"
                class="l-file-input pt-0"
                accept="image/png, image/jpeg, image/bmp"
                prepend-icon=""
                hide-input
                @change="updateAvatar"
              ></v-file-input>
              <div v-if="!fileValid" class="v-text-field__details">
                <div class="input-wrap-error">
                  <div class="v-messages theme--light error--text" role="alert">
                    <div class="v-messages__wrapper">
                      <div class="v-messages__message">
                        {{
                          $t('file_size_should_be_less_than', { value: '6 MB' })
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </v-col>
        <v-col
          class="col-12 d-flex flex-column-reverse flex-sm-column justify-center align-start"
        >
          <div class="user-settings-email body-1">
            <svg width="20" height="20" viewBox="0 0 20 20">
              <use
                :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#email-icon`"
              ></use>
            </svg>
            {{ basicInfoItem.email }}
          </div>
          <div
            v-if="isTeacher"
            class="user-settings-nickname body-1 mb-1 mb-sm-0"
          >
            <svg width="20" height="20" viewBox="0 0 20 20">
              <use
                :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#user-icon`"
              ></use>
            </svg>
            <template v-if="$vuetify.breakpoint.smAndUp">
              <nuxt-link
                :to="{ path: `/teacher/${basicInfoItem.username}` }"
                target="_blank"
              >
                {{ userLink }}
              </nuxt-link>
              <div class="input-wrap-notice text--gradient">
                {{ $t('here_is_link_to_your_profile_feel_free_to_share') }}
              </div>
            </template>
            <template v-else>
              <div class="d-flex align-center">
                <div>{{ basicInfoItem.username }}</div>
                <div class="d-flex align-center text--gradient ml-2">
                  <v-img
                    class="mr-1"
                    :src="require('~/assets/images/copy-icon-gradient.svg')"
                    width="16"
                    height="16"
                  ></v-img>
                  <input ref="profileLink" type="text" class="d-none" />
                  <div @click="copyLink">
                    {{ $t('copy_link') }}
                  </div>
                </div>
              </div>
            </template>
          </div>
        </v-col>
      </v-row>
    </div>
    <v-row>
      <v-col class="col-12 col-sm-6 mb-2 mb-md-4">
        <text-input
          :value="basicInfoItem.firstName"
          type-class="border-gradient"
          height="44"
          hide-details
          :rules="[rules.required]"
          :label="$t('first_name')"
          autocomplete="given-name"
          @input="updateValue($event, 'firstName')"
        ></text-input>
      </v-col>
      <v-col class="col-12 col-sm-6 mb-2 mb-md-4">
        <text-input
          :value="basicInfoItem.lastName"
          type-class="border-gradient"
          height="44"
          hide-details
          :rules="[rules.required]"
          :label="$t('last_name')"
          autocomplete="family-name"
          @input="updateValue($event, 'lastName')"
        ></text-input>
      </v-col>
    </v-row>
    <v-row>
      <v-col class="col-12 col-sm-6 d-flex align-end mb-2 mb-md-4">
        <div class="input-wrap">
          <div class="input-wrap-label">
            {{ $t('please_choose_your_website_language') }}
          </div>
          <user-setting-select
            :value="selectedUiLocal"
            :items="locales"
            attach-id="website-language"
            @change="updateValue($event.code, 'uiLanguage')"
          ></user-setting-select>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col class="col-12 col-sm-6 mb-2 mb-sm-0 d-flex align-end">
        <div class="input-wrap">
          <div class="input-wrap-label">
            {{ $t('select_desired_local_time_zone') }}
          </div>
          <user-setting-autocomplete
            :value="selectedTimeZone"
            :items="timeZones"
            attach-id="time-zone"
            item-text="gmt"
            :placeholder="false"
            @change="updateTimezone"
          ></user-setting-autocomplete>
        </div>
      </v-col>
      <v-col v-if="isTeacher" class="col-12 col-sm-6 d-flex align-end">
        <div class="input-wrap">
          <div class="input-wrap-label">
            {{ $t('in_which_country_do_you_normally_pay_taxes') }}
          </div>
          <user-setting-autocomplete
            :value="basicInfoItem.taxCountry"
            :items="taxCountries"
            attach-id="country-pay-taxes"
            @change="updateValue($event, 'taxCountry')"
          ></user-setting-autocomplete>
        </div>
      </v-col>
    </v-row>
  </user-setting-template>
</template>

<script>
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import UserSettingSelect from '@/components/user-settings/UserSettingSelect'
import UserSettingAutocomplete from '@/components/user-settings/UserSettingAutocomplete'
import TextInput from '@/components/form/TextInput'

export default {
  name: 'UserSettingBasic',
  components: {
    UserSettingTemplate,
    UserSettingSelect,
    UserSettingAutocomplete,
    TextInput,
  },
  props: {
    basicInfoItem: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    domain: process.env.NUXT_ENV_URL,
    file: null,
    fileValid: true,
    fileSizeLimit: 6000000,
    rules: {
      required: (v) => !!v && v.length > 1,
    },
  }),
  computed: {
    taxCountries() {
      return this.basicInfoItem.taxCountries.map((item) => item.taxCountry)
    },
    locales() {
      return this.$store.state.locales.map((item) => ({
        ...item,
        name: this.$t(item.name),
      }))
    },
    timeZones() {
      return this.basicInfoItem.timezones
    },
    selectedUiLocal() {
      return this.locales.find(
        (item) => item.code === this.basicInfoItem.uiLanguage
      )
    },
    selectedTimeZone() {
      return this.timeZones.find(
        (item) => item.name === this.basicInfoItem.timezone
      )
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    userLink() {
      return `${this.domain}/teacher/${this.basicInfoItem.username}`
    },
  },
  watch: {
    $route() {
      this.file = null
      this.fileValid = true
    },
  },
  methods: {
    getSrcAvatar(images, property, defaultImage = 'avatar.png') {
      return images?.[property]
        ? images[property]
        : require(`~/assets/images/homepage/${defaultImage}`)
    },
    getSrcSetAvatar(images, property1, property2) {
      return images?.[property1] && images?.[property2]
        ? `
            ${images[property1]} 1x,
            ${images[property2]} 2x,
          `
        : ''
    },
    updateTimezone(value) {
      if (value?.name) {
        this.updateValue(value.name, 'timezone')
      }
    },
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_BASIC_INFO_ITEM', {
        [property]: value,
      })

      // Immediately apply website language changes to i18n and cookie
      if (property === 'uiLanguage' && value) {
        try {
          if (this.$i18n?.setLocale) {
            this.$i18n.setLocale(value)
          }
          if (this.$i18n?.setLocaleCookie) {
            this.$i18n.setLocaleCookie(value)
          }
          // Also set cookie explicitly to ensure attributes and persistence
          if (this.$cookiz?.set) {
            const isSecure =
              typeof window !== 'undefined' &&
              window.location.protocol === 'https:'
            const host =
              typeof window !== 'undefined'
                ? window.location.hostname
                : undefined
            const domain =
              host && host.split('.').length > 2
                ? host.replace(/^[^.]+\./, '.')
                : host
            this.$cookiz.set('i18n_redirected', value, {
              path: '/',
              sameSite: 'lax',
              secure: isSecure,
              // Only set domain when on a public domain (avoid localhost issues)
              domain:
                domain && !/^(localhost|127\.0\.0\.1)$/.test(domain)
                  ? domain
                  : undefined,
            })
          }
          // Keep global store locale in sync
          this.$store.commit('SET_LOCALE', value)
          // Sync dayjs locale if available
          if (this.$dayjs) {
            this.$dayjs.locale(value)
          }
          // Do not modify the URL; keep routes without language prefix
        } catch (e) {
          // no-op
        }
      }
    },
    updateAvatar(file) {
      this.fileValid = true

      if (file.size > this.fileSizeLimit) {
        this.fileValid = false

        return
      }

      this.$store.dispatch('settings/updateAvatar', file)
    },
    copyLink() {
      try {
        const el = this.$refs.profileLink

        el.setAttribute('value', this.userLink)
        el.select()
        el.setSelectionRange(0, 99999)

        navigator.clipboard.writeText(el.value)

        this.$store.dispatch('snackbar/success', {
          successMessage: 'link_copied',
          timeout: 1500,
        })
      } catch (e) {
        console.log(e)
      }
    },
    submitData() {
      this.$store.dispatch('settings/updateBasicInfo').then(() => {
        this.$store.dispatch('loadingStart')
        this.$emit('reload-page')
      })
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';

.user-settings {
  &-avatar {
    .v-avatar {
      cursor: pointer;
    }
  }

  &-nickname,
  &-email {
    position: relative;
    padding-left: 32px;

    svg {
      position: absolute;
      left: 0;
      top: 2px;
    }

    a {
      color: inherit !important;
      text-decoration: none;
      transition: color 0.3s;

      &:hover {
        color: var(--v-orange-base) !important;
      }
    }
  }

  &-email {
    @media #{map-get($display-breakpoints, 'sm-and-up')} {
      margin-bottom: 12px;
    }
  }

  &-nickname {
    .input-wrap-notice {
      font-size: 12px !important;
    }
  }
}
</style>
