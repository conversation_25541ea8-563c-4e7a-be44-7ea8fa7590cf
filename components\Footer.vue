<template>
  <v-footer class="footer" color="transparent">
    <v-container fluid class="py-0">
      <v-row class="my-0">
        <v-col class="col-12 pa-0">
          <div class="footer-wrap">
            <div class="footer-bg">
              <v-img
                :src="require('~/assets/images/footer-bg.svg')"
                position="center bottom"
                :options="{ rootMargin: '50%' }"
              ></v-img>
            </div>
            <v-container>
              <v-row>
                <v-col class="col-xl-10 offset-xl-1">
                  <v-row>
                    <v-col class="col-12 col-md-10">
                      <v-row>
                        <v-col class="col-6 col-md-3 py-0">
                          <div class="footer-col">
                            <div class="footer-col-header">
                              {{ $t('langu') }}
                            </div>
                            <nav class="footer-nav">
                              <div class="footer-nav-item">
                                <nuxt-link to="/">
                                  {{ $t('homepage') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  :to="localePath({ path: '/about-us' })"
                                >
                                  {{ $t('about_us') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link :to="localePath({ path: '/faq' })">
                                  {{ $t('faqs') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/business">
                                  {{ $t('langu_for_companies') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/education">
                                  {{ $t('langu_for_education') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <a @click="goToBlog">
                                  {{ $t('blog') }}
                                </a>
                              </div>
                              <div class="footer-nav-item">
                                <a
                                  href="https://heylangu-teachers.com"
                                  target="_blank"
                                >
                                  {{ $t('apply_as_teacher') }}
                                </a>
                              </div>
                              <div class="footer-nav-item">
                                <a href="mailto:<EMAIL>">
                                  {{ $t('contact_us') }}
                                </a>
                              </div>
                              <div class="footer-nav-item">
                                <a
                                  href="https://heylangu.com/privacy-policy"
                                  target="_blank"
                                >
                                  {{ $t('privacy_policy') }}
                                </a>
                              </div>
                              <div class="footer-nav-item">
                                <a href="/terms/student">
                                  {{ $t('student_terms_and_conditions') }}</a
                                >
                              </div>
                              <div class="footer-nav-item">
                                <a href="/terms/teacher">{{
                                  $t('teacher_terms_and_conditions')
                                }}</a>
                              </div>
                            </nav>
                          </div>
                        </v-col>
                        <v-col class="col-6 col-md-3 py-0">
                          <div class="footer-col">
                            <div class="footer-col-header">
                              {{ $t('languages') }}
                            </div>
                            <nav class="footer-nav">
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,12">{{
                                  $t('english_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,16">{{
                                  $t('french_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,44">{{
                                  $t('spanish_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,17">{{
                                  $t('german_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,23">{{
                                  $t('italian_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,7">{{
                                  $t('chinese_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,34">{{
                                  $t('brazilian_portuguese_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,35">{{
                                  $t('portuguese_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,32">{{
                                  $t('polish_teachers_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,12">{{
                                  $t('british_english_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers/1/language,12">{{
                                  $t('american_english_online')
                                }}</nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  :to="localePath({ name: 'teachers' })"
                                >
                                  {{ $t('see_more') }}
                                </nuxt-link>
                              </div>
                            </nav>
                          </div>
                        </v-col>
                        <v-col class="col-6 col-md-3 py-0">
                          <div class="footer-col">
                            <div class="footer-col-header">
                              {{ $t('specialities') }}
                            </div>
                            <nav class="footer-nav">
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,1;speciality,15"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_speaking_and_conversation'
                                    )
                                  }}</nuxt-link
                                >
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,22"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_business'
                                    )
                                  }}</nuxt-link
                                >
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,39"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_finance_and_banking'
                                    )
                                  }}</nuxt-link
                                >
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,23"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_marketing'
                                    )
                                  }}</nuxt-link
                                >
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,24"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_it_industry'
                                    )
                                  }}</nuxt-link
                                >
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,27"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_engineering'
                                    )
                                  }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,26
"
                                  >{{
                                    $t('private_english_lessons_online_for_law')
                                  }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,34"
                                  >{{
                                    $t(
                                      'private_english_lessons_online_for_tourism'
                                    )
                                  }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,29"
                                >
                                  {{ $t('language_teachers_for_media') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/motivation,2;speciality,38"
                                >
                                  {{ $t('language_teachers_for_economics') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link to="/teachers">
                                  {{ $t('see_more') }}
                                </nuxt-link>
                              </div>
                            </nav>
                          </div>
                        </v-col>
                        <v-col class="col-6 col-md-3 py-0">
                          <div class="footer-col">
                            <div class="footer-col-header">
                              {{ $t('exam_preparation') }}
                            </div>
                            <nav class="footer-nav">
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,1"
                                >
                                  {{ $t('ielts') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,3"
                                >
                                  {{ $t('toefl') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,8"
                                >
                                  {{ $t('fce_b2_first') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,2"
                                >
                                  {{ $t('cae_c1_advanced') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,4"
                                >
                                  {{ $t('cpe_c2_proficiency') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,13"
                                >
                                  {{ $t('matura_exam_poland') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,40"
                                >
                                  {{ $t('spanish_exam_dele') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,44"
                                >
                                  {{ $t('french_exam_dele') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,48"
                                >
                                  {{ $t('oet') }}
                                </nuxt-link>
                              </div>
                              <div class="footer-nav-item">
                                <nuxt-link
                                  to="/teachers/1/language,12;motivation,3;speciality,14"
                                >
                                  {{ $t('gcses') }}
                                </nuxt-link>
                              </div>
                            </nav>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col class="col-12 col-md-2">
                      <v-row>
                        <v-col class="col-12 py-0">
                          <div class="footer-col footer-col-s">
                            <div class="footer-col-header">
                              {{ $t('follow_us') }}
                            </div>
                            <nav class="footer-nav footer-nav-s">
                              <div class="footer-nav-item mb-0 mb-md-3">
                                <a
                                  href="https://www.facebook.com/heylangu"
                                  target="_blank"
                                  rel="noopener"
                                >
                                  <svg
                                    width="32"
                                    height="32"
                                    viewBox="0 0 32 32"
                                  >
                                    <use
                                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#facebook`"
                                    ></use>
                                  </svg>
                                </a>
                              </div>
                              <!--                              <div class="footer-nav-item mb-3">-->
                              <!--                                <a-->
                              <!--                                  href="https://twitter.com/heylangu"-->
                              <!--                                  target="_blank"-->
                              <!--                                  rel="noopener"-->
                              <!--                                >-->
                              <!--                              <svg width="32" height="32" viewBox="0 0 32 32">-->
                              <!--                                <use-->
                              <!--                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#twitter`"-->
                              <!--                                ></use>-->
                              <!--                              </svg>-->
                              <!--                                </a>-->
                              <!--                              </div>-->
                              <div class="footer-nav-item mb-0 mb-md-3">
                                <a
                                  href="https://www.linkedin.com/company/langu/"
                                  target="_blank"
                                  rel="noopener"
                                >
                                  <svg
                                    width="32"
                                    height="32"
                                    viewBox="0 0 32 32"
                                  >
                                    <use
                                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#linkedin`"
                                    ></use>
                                  </svg>
                                </a>
                              </div>
                              <div class="footer-nav-item">
                                <a
                                  href="https://www.instagram.com/heylangu/"
                                  target="_blank"
                                  rel="noopener"
                                >
                                  <svg
                                    width="32"
                                    height="32"
                                    viewBox="0 0 32 32"
                                  >
                                    <use
                                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#instagram`"
                                    ></use>
                                  </svg>
                                </a>
                              </div>
                            </nav>
                          </div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col class="col-12 py-0">
                  <div class="footer-bottom text-center">
                    © Langu™ (E-Polyglot Ltd) {{ new Date().getFullYear() }}.
                    All rights reserved. <br />
                    152-160 City Road, London EC1V 2NX <br />
                    Made with ❤️ in London, Berlin & Warsaw.
                  </div>
                </v-col>
              </v-row>
            </v-container>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-footer>
</template>

<script>
export default {
  name: 'Footer',
  methods: {
    goToBlog() {
      window.location.href = '/blog'
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/_vars';

.footer {
  padding: 0 !important;
  font-size: 13px;
  color: #fff !important;

  &-wrap {
    position: relative;
    margin-top: 168px;
    padding: 55px 0 30px;
    background-color: var(--v-darkLight-base);

    @media only screen and (max-width: $xxs-and-down) {
      margin-top: 130px;
    }
  }

  &-bg {
    position: absolute;
    width: 100%;
    height: 178px;
    left: 0;
    top: -178px;

    & > div {
      height: 178px;
    }

    @media only screen and (min-width: $hd-and-up) {
      .v-image__image {
        background-size: 100% 100%;
      }
    }
  }

  &-col {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      margin-bottom: 45px;

      &-s {
        text-align: center;

        .footer-col-header {
          border-bottom: none;
        }

        .footer-nav {
          max-width: 100%;
          display: flex;
          justify-content: center;

          .footer-nav-item {
            margin-left: 12px !important;
            margin-right: 12px !important;
          }
        }
      }
    }

    &-header {
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  &-nav {
    max-width: 210px;

    &-item {
      margin-top: 9px;

      a {
        text-decoration: none;
        color: rgba(255, 255, 255, 0.5) !important;
        transition: color 0.2s;

        &:hover {
          color: var(--v-orange-base) !important;
        }
      }
    }
  }

  &-bottom {
    padding-top: 68px;
    font-size: 10px;
    line-height: 2;
    letter-spacing: 0.1px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      padding-top: 20px;
    }
  }
}

.teacher-profile-page .footer-wrap {
  @media #{map-get($display-breakpoints, 'sm-and-down')} {
    padding-bottom: 132px;
  }

  @media only screen and (max-width: $xsm-and-down) {
    padding-bottom: 112px;
  }
}
</style>
