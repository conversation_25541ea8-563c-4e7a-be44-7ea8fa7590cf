export default function ({ route, redirect, store, app }) {
  // Handle teacher listing URL structure: /teachers/{speciality}-{language-to-learn}
  // This works for all site languages (en without prefix, pl and es with prefix)

  // First, handle language-only paths like /pl or /es (redirect to /pl/teachers or /es/teachers)
  const languageOnlyPattern = /^\/(pl|es)\/?$/
  const languageOnlyMatch = route.path.match(languageOnlyPattern)

  if (languageOnlyMatch) {
    // For language-only URLs, redirect to the teachers page in that language
    // This allows /pl and /es to work as expected
    return // Let nuxt-i18n handle language-only routes
  }

  // Store URL parameters for /teachers pages
  // Pattern: /teachers or /{pl|es}/teachers or /teachers/{spec}-{lang} or /{pl|es}/teachers/{spec}-{lang}
  // Also handle single parameter cases: /teachers/{spec} or /teachers/{lang}
  const teachersPattern = /^(?:\/(pl|es))?(\/teachers(?:\/([a-z-]+)(?:-([a-z-]+))?)?(?:\/(.*))?)?$/
  const teachersMatch = route.path.match(teachersPattern)

  if (teachersMatch && teachersMatch[2]) {
    // [2] is the /teachers part
    const [
      ,
      siteLanguage,
      ,
      firstParam,
      secondParam,
      additionalPath,
    ] = teachersMatch

    // Store URL parameters for use in components
    if (firstParam && secondParam) {
      // Both parameters present: speciality-language format
      store.commit('SET_URL_PARAMS', {
        siteLanguage: siteLanguage || 'en',
        speciality: firstParam,
        languageToLearn: secondParam,
        additionalPath: additionalPath || null,
      })
    } else if (firstParam) {
      // Single parameter: need to determine if it's speciality or language
      // For now, we'll store it as speciality and let the backend/frontend logic determine
      store.commit('SET_URL_PARAMS', {
        siteLanguage: siteLanguage || 'en',
        speciality: firstParam,
        languageToLearn: null,
        additionalPath: additionalPath || null,
      })
    } else {
      // Reset URL params if on /teachers without filters
      store.commit('SET_URL_PARAMS', {
        siteLanguage: siteLanguage || 'en',
        speciality: null,
        languageToLearn: null,
        additionalPath: null,
      })
    }
  }

  // Handle legacy /teacher-listing redirects to new /teachers structure
  const legacyPattern = /^(?:\/(pl|es))?(\/teacher-listing(?:\/(.*))?)?$/
  const legacyMatch = route.path.match(legacyPattern)

  if (legacyMatch && legacyMatch[2]) {
    // [2] is the /teacher-listing part
    const [, siteLanguage, , additionalPath] = legacyMatch
    const basePath = siteLanguage ? `/${siteLanguage}/teachers` : '/teachers'
    const newPath = additionalPath ? `${basePath}/${additionalPath}` : basePath

    return redirect(301, newPath)
  }

  // Handle legacy subdomain redirects to new URL structure (only on client side)
  if (process.client) {
    const host = window.location.host
    const subdomain = host.split('.')[0]

    // If we're on a language subdomain (pl.langu.io or es.langu.io), redirect to path-based structure
    if (['pl', 'es'].includes(subdomain) && host.includes('.')) {
      const mainDomain = host.split('.').slice(1).join('.')
      const newUrl = `https://${mainDomain}/${subdomain}${route.fullPath}`
      window.location.href = newUrl
    }
  }
}
