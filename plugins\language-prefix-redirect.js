/**
 * Plugin to handle language prefix redirects for logged-in users
 * This runs on the client side and watches for route changes
 */

export default function ({ app, store, redirect }) {
  if (!process.client) {
    return
  }

  // Watch for route changes and redirect if needed
  app.router.afterEach((to, from) => {
    try {
      const isUserLogged = store.getters['user/isUserLogged']
      const toPath = to.path
      const hasLanguagePrefix = /^\/(pl|es)\//.test(toPath)
      const pathWithoutPrefix = toPath.replace(/^\/(pl|es)/, '') || '/'

      if (isUserLogged && hasLanguagePrefix) {
        console.log(
          `[LanguagePrefixRedirect] Logged-in user on prefixed URL. Redirecting from ${toPath} to ${pathWithoutPrefix}`
        )
        // Use window.location to force a hard redirect
        window.location.href = pathWithoutPrefix
      }
    } catch (error) {
      console.error('[LanguagePrefixRedirect] Error:', error)
    }
  })
}

