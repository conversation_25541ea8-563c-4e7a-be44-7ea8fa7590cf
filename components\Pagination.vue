<template>
  <nav class="pagination">
    <ul :key="key" class="pagination-list d-flex justify-center align-center">
      <li
        :class="['pagination-item pagination-item-prev']"
        @click="prevPageClickHandler"
      >
        <div class="icon next-prev-icon">
          <svg width="17" height="12" viewBox="0 0 17 12">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#arrow-prev`"
            ></use>
          </svg>
        </div>
        <span class="d-none d-sm-inline ml-2">{{ $t('previous') }}</span>
      </li>
      <li v-for="(page, index) in pages" :key="index" class="pagination-item">
        <template v-if="page !== 0">
          <nuxt-link
            :to="getUrl(page)"
            :class="{ current: currentPage === page }"
          >
            {{ page }}
          </nuxt-link>
        </template>
        <template v-else>
          <span class="dots">...</span>
        </template>
      </li>
      <li
        :class="['pagination-item pagination-item-next']"
        @click="nextPageClickHandler"
      >
        <span class="d-none d-sm-inline mr-2">{{ $t('next') }}</span>
        <div class="icon">
          <svg width="17" height="12" viewBox="0 0 17 12">
            <use
              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#arrow-next`"
            ></use>
          </svg>
        </div>
      </li>
    </ul>
  </nav>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    currentPage: {
      type: Number,
      required: true,
    },
    totalPages: {
      type: Number,
      required: true,
    },
    route: {
      type: String,
      required: true,
    },
    params: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      key: 1,
    }
  },
  computed: {
    isFirstCurrentPage() {
      return this.currentPage <= 1
    },
    isLastCurrentPage() {
      return this.currentPage >= this.totalPages
    },
    pages() {
      const pages = []

      for (let i = 1; i <= this.totalPages; i++) {
        pages.push(i)
      }

      let pagination = pages.slice()

      if (this.totalPages > 6) {
        let left = []
        let right = []
        let center = []

        if (this.currentPage < 3 || this.currentPage > this.totalPages - 3) {
          left = pages.slice(0, 3)
          right = pages.slice(-3)

          pagination = [...left, 0, ...right]
        }

        if (this.currentPage === 3) {
          left = pages.slice(0, 5)
          right = pages.slice(-1)

          pagination = [...left, 0, ...right]
        }

        if (this.currentPage > 3 && this.currentPage < this.totalPages - 2) {
          left = pages.slice(0, 1)
          right = pages.slice(-1)
          center = pages.slice(this.currentPage - 2, this.currentPage + 1)

          pagination = [...left, 0, ...center, 0, ...right]
        }

        if (this.currentPage === this.totalPages - 2) {
          left = pages.slice(0, 1)
          right = pages.slice(-5)

          pagination = [...left, 0, ...right]
        }
      }

      return pagination
    },
    queryStr() {
      const { query } = this.$route
      const keys = Object.keys(query)

      let str = ''

      if (keys.length) {
        str += '?'

        for (let i = 0; i < keys.length; i++) {
          str += `${keys[i]}=${query[keys[i]]}`

          if (i < keys.length - 1) {
            str += '&'
          }
        }
      }

      return str
    },
  },
  watch: {
    currentPage() {
      this.key++
    },
  },
  methods: {
    getUrl(page) {
      let url = this.route

      if (page > 1 || this.params.length) {
        url += `/${page}${this.params.length ? '/' + this.params : ''}`
      }

      if (this.queryStr.length) {
        url += this.queryStr
      }

      return url
    },
    prevPageClickHandler() {
      if (!this.isFirstCurrentPage) {
        this.$router.push({ path: this.getUrl(this.currentPage - 1) })
      }
    },
    nextPageClickHandler() {
      if (!this.isLastCurrentPage) {
        this.$router.push({ path: this.getUrl(this.currentPage + 1) })
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.pagination {
  &-list {
    padding-left: 0;
    list-style-type: none;
    flex-wrap: wrap;
  }

  &-item {
    a {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 35px;
      height: 35px;
      font-size: 16px;
      font-weight: 700;
      border-radius: 4px;
      color: var(--v-darkLight-base);
      text-decoration: none;
      transition: color 0.3s;
      margin: 0px 10px;

      @media only screen and (max-width: $xsm-and-down) {
        width: 38px;
        height: 38px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: 36px;
        height: 36px;
        font-size: 14px;
        border-radius: 2px;
      }

      &.current {
        background: var(--v-orange-base);
      }

      &:not(.current):hover {
        color: var(--v-orange-base);
      }
    }

    &-prev,
    &-next {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      border-radius: 50%;
      transition: color 0.3s;

      &.disabled {
        opacity: 0.6;
      }

      &:not(.disabled) {
        cursor: pointer;

        &:hover {
          color: var(--v-orange-base);
        }
      }
    }

    &-prev {
      margin-right: 15px;

      @media only screen and (max-width: $xsm-and-down) {
        margin-right: 10px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        margin-right: 5px;
      }

      .icon {
        margin-right: 12px;
      }
    }

    &-next {
      margin-left: 15px;

      @media only screen and (max-width: $xsm-and-down) {
        margin-left: 10px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        margin-left: 5px;
      }

      .icon {
        margin-left: 12px;
      }
    }

    .dots {
      display: inline-block;
      width: 64px;
      text-align: center;

      @media only screen and (max-width: $xsm-and-down) {
        width: 30px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: 25px;
      }
    }
  }
}

.pagination-item-prev {
  color: black;
  fill: black;
}

.pagination-item-prev span {
  color: black;
  fill: black;
  font-weight: bold;
}
.pagination-item-next {
  color: black;
  fill: black;
}

.pagination-item-next span {
  color: black;
  fill: black;
  font-weight: bold;
}
</style>
