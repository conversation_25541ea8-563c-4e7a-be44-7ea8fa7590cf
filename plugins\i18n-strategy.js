/* eslint-disable no-lonely-if */
/**
 * Plugin to dynamically handle language prefix routing based on login status
 *
 * For non-logged-in users: URLs should have language prefix for pl and es
 * For logged-in users: URLs should NOT have language prefix
 *
 * NOTE: Router plugin handles logged-in user redirects (priority)
 * This plugin handles non-logged-in user prefix management
 */

export default function ({ store, app, route, redirect }) {
  // Only run on client side to avoid SSR issues
  if (!process.client) {
    return
  }

  const isUserLogged = store.getters['user/isUserLogged']
  const currentLocale = app.i18n.locale
  const currentPath = route.path

  // Check if current path has a language prefix
  const hasLanguagePrefix = /^\/(pl|es)\//.test(currentPath)
  const pathLanguagePrefix = currentPath.match(/^\/(pl|es)\//)?.[1]

  // Get path without language prefix
  const pathWithoutPrefix = currentPath.replace(/^\/(pl|es)/, '') || '/'

  // Only handle non-logged-in users here (router plugin handles logged-in users)
  if (!isUserLogged) {
    // For non-logged-in users: ensure language prefix is present for pl and es
    if (currentLocale !== 'en') {
      // Should have prefix
      if (!hasLanguagePrefix || pathLanguagePrefix !== currentLocale) {
        // Redirect to correct prefixed URL
        const newPath = `/${currentLocale}${pathWithoutPrefix}`
        if (newPath !== currentPath) {
          console.log(
            `[i18n-strategy] Non-logged-in user. Redirecting from ${currentPath} to ${newPath}`
          )
          redirect(newPath)
        }
      }
    } else {
      // English should NOT have prefix
      if (hasLanguagePrefix) {
        console.log(
          `[i18n-strategy] Non-logged-in user on English. Redirecting from ${currentPath} to ${pathWithoutPrefix}`
        )
        redirect(pathWithoutPrefix)
      }
    }
  }
}
