/**
 * Comprehensive Test Suite for Whereby Integration
 * Tests all the new features and optimizations
 */

const https = require('https')

// Test configuration
const BASE_URL = 'http://localhost:3000/_nuxt/api/whereby'
const TEST_LESSON_ID = 'test-lesson-' + Date.now()
const TEST_TEACHER_ID = 'teacher-123'
const TEST_STUDENT_ID = 'student-456'

// Helper function to make HTTP requests
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url)
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    }

    const protocol = urlObj.protocol === 'https:' ? https : require('http')
    const req = protocol.request(requestOptions, (res) => {
      let data = ''
      res.on('data', (chunk) => {
        data += chunk
      })
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null,
          }
          resolve(response)
        } catch (error) {
          reject(new Error('JSON Parse Error: ' + error.message))
        }
      })
    })

    req.on('error', reject)

    if (options.data) {
      req.write(JSON.stringify(options.data))
    }

    req.end()
  })
}

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...')
  try {
    const response = await makeRequest(`${BASE_URL}/health`)
    console.log('✅ Health check passed:', response.data)
    return true
  } catch (error) {
    console.error('❌ Health check failed:', error.message)
    return false
  }
}

async function testRoomCreation() {
  console.log('\n🏗️ Testing Room Creation...')
  try {
    const response = await makeRequest(`${BASE_URL}/create`, {
      method: 'POST',
      data: {
        lessonId: TEST_LESSON_ID,
        teacherId: TEST_TEACHER_ID,
        studentId: TEST_STUDENT_ID,
      },
    })

    if (response.status === 200 && response.data.success) {
      console.log('✅ Room created successfully:', {
        lessonId: response.data.room.lessonId,
        meetingId: response.data.room.meetingId,
        responseTime: response.data.responseTime,
      })
      return response.data.room
    } else {
      console.error('❌ Room creation failed:', response.data)
      return null
    }
  } catch (error) {
    console.error('❌ Room creation error:', error.message)
    return null
  }
}

async function testRoomRetrieval() {
  console.log('\n📋 Testing Room Retrieval...')
  try {
    const response = await makeRequest(`${BASE_URL}/room/${TEST_LESSON_ID}`)

    if (response.status === 200 && response.data.success) {
      console.log('✅ Room retrieved successfully:', {
        lessonId: response.data.room.lessonId,
        isActive: response.data.isActive,
      })
      return true
    } else {
      console.error('❌ Room retrieval failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Room retrieval error:', error.message)
    return false
  }
}

async function testLessonScheduling() {
  console.log('\n📅 Testing Lesson Scheduling...')
  try {
    const scheduledTime = new Date(Date.now() + 25 * 60 * 1000) // 25 minutes from now
    const response = await makeRequest(`${BASE_URL}/schedule-lesson`, {
      method: 'POST',
      data: {
        lessonId: 'scheduled-lesson-' + Date.now(),
        scheduledTime: scheduledTime.toISOString(),
        teacherId: TEST_TEACHER_ID,
        studentId: TEST_STUDENT_ID,
      },
    })

    if (response.status === 200 && response.data.success) {
      console.log('✅ Lesson scheduled successfully:', {
        lessonId: response.data.lessonId,
        scheduledTime: response.data.scheduledTime,
      })
      return response.data.lessonId
    } else {
      console.error('❌ Lesson scheduling failed:', response.data)
      return null
    }
  } catch (error) {
    console.error('❌ Lesson scheduling error:', error.message)
    return null
  }
}

async function testPrecreationStats() {
  console.log('\n📊 Testing Pre-creation Stats...')
  try {
    const response = await makeRequest(`${BASE_URL}/precreation-stats`)

    if (response.status === 200 && response.data.success) {
      console.log('✅ Pre-creation stats retrieved:', {
        isRunning: response.data.precreation.isRunning,
        scheduledLessons: response.data.precreation.scheduledLessons,
        config: response.data.precreation.config,
      })
      return true
    } else {
      console.error('❌ Pre-creation stats failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Pre-creation stats error:', error.message)
    return false
  }
}

async function testMonitoring() {
  console.log('\n📈 Testing Monitoring...')
  try {
    const response = await makeRequest(`${BASE_URL}/monitoring`)

    if (response.status === 200 && response.data.success) {
      const monitoring = response.data.monitoring
      console.log('✅ Monitoring data retrieved:', {
        isHealthy: monitoring.metrics.isHealthy,
        roomsCreated: monitoring.metrics.roomsCreated,
        errorRate: monitoring.metrics.errorRate,
        uptime: monitoring.metrics.uptimeFormatted,
        healthChecks: Object.keys(monitoring.healthChecks),
      })
      return true
    } else {
      console.error('❌ Monitoring failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Monitoring error:', error.message)
    return false
  }
}

async function testStats() {
  console.log('\n📊 Testing Stats...')
  try {
    const response = await makeRequest(`${BASE_URL}/stats`)

    if (response.status === 200 && response.data.success) {
      console.log('✅ Stats retrieved:', {
        activeRooms: response.data.activeRooms,
        cleanupRuns: response.data.stats.totalCleanupRuns,
        roomsCreated: response.data.stats.totalRoomsCreated,
        storageType: response.data.storage?.type || 'memory',
      })
      return true
    } else {
      console.error('❌ Stats failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Stats error:', error.message)
    return false
  }
}

async function testCleanup() {
  console.log('\n🧹 Testing Manual Cleanup...')
  try {
    const response = await makeRequest(`${BASE_URL}/cleanup`, {
      method: 'POST',
    })

    if (response.status === 200 && response.data.success) {
      console.log('✅ Manual cleanup completed:', {
        expiredRooms: response.data.expiredRooms,
        remainingRooms: response.data.remainingRooms,
      })
      return true
    } else {
      console.error('❌ Manual cleanup failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Manual cleanup error:', error.message)
    return false
  }
}

async function testRoomDeletion() {
  console.log('\n🗑️ Testing Room Deletion...')
  try {
    const response = await makeRequest(`${BASE_URL}/delete/${TEST_LESSON_ID}`, {
      method: 'DELETE',
    })

    if (response.status === 200 && response.data.success) {
      console.log('✅ Room deleted successfully:', {
        lessonId: response.data.lessonId,
        responseTime: response.data.responseTime,
      })
      return true
    } else {
      console.error('❌ Room deletion failed:', response.data)
      return false
    }
  } catch (error) {
    console.error('❌ Room deletion error:', error.message)
    return false
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Whereby Integration Tests...')
  console.log('=' .repeat(50))

  const results = {
    passed: 0,
    failed: 0,
    total: 0,
  }

  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Room Creation', fn: testRoomCreation },
    { name: 'Room Retrieval', fn: testRoomRetrieval },
    { name: 'Lesson Scheduling', fn: testLessonScheduling },
    { name: 'Pre-creation Stats', fn: testPrecreationStats },
    { name: 'Monitoring', fn: testMonitoring },
    { name: 'Stats', fn: testStats },
    { name: 'Manual Cleanup', fn: testCleanup },
    { name: 'Room Deletion', fn: testRoomDeletion },
  ]

  for (const test of tests) {
    results.total++
    try {
      const success = await test.fn()
      if (success) {
        results.passed++
      } else {
        results.failed++
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" threw an error:`, error.message)
      results.failed++
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  console.log('\n' + '=' .repeat(50))
  console.log('📋 Test Results Summary:')
  console.log(`✅ Passed: ${results.passed}`)
  console.log(`❌ Failed: ${results.failed}`)
  console.log(`📊 Total: ${results.total}`)
  console.log(`🎯 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`)

  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! The Whereby integration is working correctly.')
  } else {
    console.log('\n⚠️ Some tests failed. Please check the implementation.')
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = { runTests }
