<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
      :page="page"
    ></teacher-listing>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'
import { processUrlParameters } from '~/helpers/processUrlParameters'

export default {
  name: 'TeacherListingPage',
  components: { TeacherListing },
  async asyncData({ store, params, query, redirect, req }) {
    console.log('[DEBUG] asyncData called')
    let filters

    // Set flag to prevent navigation during initial load
    store.commit('teacher_filter/SET_LOADING_FROM_URL', true)

    // Only fetch filters if not already loaded
    if (!store.state.teacher_filter.filters) {
      await store
        .dispatch('teacher_filter/getFilters', { skipAutoSelect: true })
        .then((data) => (filters = data))
    } else {
      filters = store.state.teacher_filter.filters
    }

    // DON'T reset filters - they should persist based on URL params

    // Check if params.page is actually a filter parameter (language or speciality)
    const pageParam = params.page
    const isNumericPage = /^\d+$/.test(pageParam)

    let page = 1
    // let isSingleFilterParam = false

    if (!isNumericPage) {
      // This is a filter parameter (language or speciality), not a page number
      // Use page 1 and process the param as a filter
      page = 1
      // isSingleFilterParam = true

      // Store this in urlParams if not already set by middleware
      const urlParams = store.getters.urlParams
      if (!urlParams.speciality && !urlParams.languageToLearn) {
        store.commit('SET_URL_PARAMS', {
          siteLanguage: 'en',
          speciality: pageParam,
          languageToLearn: null,
          additionalPath: null,
        })
      }
    } else {
      page = +params.page
    }
    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const searchQuery = query?.search

    let paramsStr = `currency,${currentCurrency.id};sortOption,${selectedSorting.id}`

    // Process all query parameters comprehensively
    if (query) {
      processUrlParameters(query, filters, store)

      // Build params string for API call
      const appliedKeys = new Set(['currency', 'sortOption'])
      Object.keys(query).forEach((key) => {
        if (key !== 'search' && query[key] && !appliedKeys.has(key)) {
          paramsStr += `;${key},${query[key]}`
          appliedKeys.add(key)
        }
      })
    }

    // Check for URL parameters from new URL structure
    const urlParams = store.getters.urlParams
    console.log('[DEBUG] urlParams:', urlParams)
    console.log('[DEBUG] query params:', query)
    const { getSpecialityId, getLanguageId } = require('~/helpers/urlParams')

    // Check for specialty in cookie if not in URL
    let specialtyFromCookie = null
    if (!urlParams.speciality && !query.speciality) {
      // Try to get cookie from server-side (req) or client-side (window)
      if (req && req.headers && req.headers.cookie) {
        // Server-side: parse cookie from request headers
        const cookieMatch = req.headers.cookie.match(
          /selected_specialty=([^;]+)/
        )
        if (cookieMatch) {
          specialtyFromCookie = cookieMatch[1]
          console.log(
            '[DEBUG] Found specialty cookie on server:',
            specialtyFromCookie
          )
        }
      } else if (process.client) {
        // Client-side: use the getter
        specialtyFromCookie = store.getters['teacher_filter/getSpecialtyCookie']
        console.log(
          '[DEBUG] Found specialty cookie on client:',
          specialtyFromCookie
        )
      }
    }

    // Handle specialty from cookie separately
    if (specialtyFromCookie && !urlParams.speciality) {
      console.log('[DEBUG] Loading specialty from cookie:', specialtyFromCookie)
      const specialtyIds = specialtyFromCookie
        .split(',')
        .map((id) => parseInt(id))
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      const specialities = allSpecialities.filter((s) =>
        specialtyIds.includes(s.id)
      )
      console.log('[DEBUG] Found specialities from cookie:', specialities)
      if (specialities.length > 0) {
        store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
          specialities,
          updateActiveFilters: true,
        })
        console.log('[DEBUG] Set specialities in store from cookie')
      }
    }

    if (urlParams.speciality || urlParams.languageToLearn) {
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      let specialityId = null
      let languageId = null
      const specialityToProcess = urlParams.speciality

      // Try to match speciality first (if present)
      if (specialityToProcess) {
        console.log(
          '[DEBUG] Processing speciality parameter:',
          specialityToProcess
        )

        // First try to match as a single specialty
        specialityId = getSpecialityId(specialityToProcess, allSpecialities)
        console.log('[DEBUG] Single specialty match:', specialityId)

        // If no match as single specialty, try to parse as specialty-language combination
        if (!specialityId) {
          console.log(
            '[DEBUG] No single specialty match, trying to parse combination'
          )

          // Try to split by common separators and match each part
          const possibleParts = specialityToProcess.split(/[-_]/)
          console.log('[DEBUG] Possible parts:', possibleParts)

          for (let i = 0; i < possibleParts.length; i++) {
            const part = possibleParts[i]
            console.log('[DEBUG] Trying part as specialty:', part)

            // Try this part as specialty
            const partSpecialityId = getSpecialityId(part, allSpecialities)
            if (partSpecialityId) {
              specialityId = partSpecialityId
              console.log(
                '[DEBUG] Found specialty match:',
                part,
                '->',
                specialityId
              )

              // Try remaining parts as language
              const remainingParts = possibleParts.slice(i + 1).join('-')
              if (remainingParts) {
                console.log(
                  '[DEBUG] Trying remaining parts as language:',
                  remainingParts
                )
                const partLanguageId = getLanguageId(
                  remainingParts,
                  filters?.languages || []
                )
                if (partLanguageId) {
                  languageId = partLanguageId
                  console.log(
                    '[DEBUG] Found language match:',
                    remainingParts,
                    '->',
                    languageId
                  )
                  break
                }
              }
              break
            }
          }
        }

        // If still no match as specialty and no second param, try as language
        if (!specialityId && !urlParams.languageToLearn) {
          languageId = getLanguageId(
            urlParams.speciality,
            filters?.languages || []
          )
          if (languageId) {
            console.log('[DEBUG] Found as language only:', languageId)
            // It's actually a language, not a speciality
            store.commit('SET_URL_PARAMS', {
              siteLanguage: urlParams.siteLanguage,
              speciality: null,
              languageToLearn: urlParams.speciality,
              additionalPath: urlParams.additionalPath,
            })
          }
        }
      }

      // Try to match language (if present)
      if (urlParams.languageToLearn) {
        languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )
      }

      if (specialityId) {
        const selectedSpeciality = allSpecialities.find(
          (spec) => spec.id === specialityId
        )
        if (selectedSpeciality) {
          console.log(
            '[DEBUG] _page/index.vue - Setting speciality from URL:',
            selectedSpeciality
          )
          store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
            specialities: [selectedSpeciality],
            updateActiveFilters: true, // Need this for chips and active filters
          })
          // Save specialty to cookie for persistence
          console.log('[DEBUG] _page/index.vue - Calling saveSpecialtyCookie')
          store.dispatch('teacher_filter/saveSpecialtyCookie')
          paramsStr += `;speciality,${specialityId}`
        }
      }

      if (languageId) {
        const selectedLanguage = filters.languages.find(
          (lang) => lang.id === languageId
        )
        if (selectedLanguage) {
          store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language: selectedLanguage,
            updateActiveFilters: true, // Need this for chips and active filters
          })
          paramsStr += `;language,${languageId}`
        }
      }
    } else if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: false,
      })
    } else if (store.getters['user/isStudent']) {
      const userLanguage = store.getters['user/language']

      if (userLanguage) {
        store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
          language: userLanguage,
          updateActiveFilters: false,
        })

        paramsStr += `;language,${userLanguage.id}`
      }
    }

    // Redirect to new URL format if we have old URL parameters
    if (urlParams.speciality || urlParams.languageToLearn) {
      const { generateNewUrl } = require('~/helpers/urlParams')

      // Get current language and filters for URL generation
      const siteLanguage = store.state.locale || 'en'
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      // Check if we can generate a new URL
      if (urlParams.speciality && urlParams.languageToLearn) {
        const specialityId = getSpecialityId(
          urlParams.speciality,
          allSpecialities
        )
        const languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )

        if (specialityId && languageId) {
          const newUrl = generateNewUrl(
            { speciality: specialityId, language: languageId },
            siteLanguage,
            allSpecialities,
            filters?.languages || []
          )

          // Get additional parameters for query string
          const additionalParams = {}
          if (urlParams.sortOption)
            additionalParams.sortOption = urlParams.sortOption
          if (urlParams.currency) additionalParams.currency = urlParams.currency
          if (urlParams.motivation)
            additionalParams.motivation = urlParams.motivation
          if (urlParams.proficiencyLevels)
            additionalParams.proficiencyLevels = urlParams.proficiencyLevels
          if (urlParams.teacherPreference)
            additionalParams.teacherPreference = urlParams.teacherPreference
          if (urlParams.matchLanguages)
            additionalParams.matchLanguages = urlParams.matchLanguages
          if (urlParams.dates) additionalParams.dates = urlParams.dates
          if (urlParams.time) additionalParams.time = urlParams.time
          if (urlParams.tag) additionalParams.tag = urlParams.tag

          // Redirect to new URL format
          return redirect(301, {
            path: newUrl,
            query: additionalParams,
          })
        }
      } else if (urlParams.languageToLearn && !urlParams.speciality) {
        // Only language selected - redirect to /teachers/language format
        const languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )

        if (languageId) {
          const language = filters?.languages?.find(
            (lang) => lang.id === languageId
          )
          if (language) {
            const languageName = language.name
              .toLowerCase()
              .replace(/\s+/g, '-')
            const baseUrl = siteLanguage === 'en' ? '' : `/${siteLanguage}`
            const newUrl = `${baseUrl}/teachers/${languageName}`

            // Get additional parameters for query string
            const additionalParams = {}
            if (urlParams.sortOption)
              additionalParams.sortOption = urlParams.sortOption
            if (urlParams.currency)
              additionalParams.currency = urlParams.currency
            if (urlParams.motivation)
              additionalParams.motivation = urlParams.motivation
            if (urlParams.proficiencyLevels)
              additionalParams.proficiencyLevels = urlParams.proficiencyLevels
            if (urlParams.teacherPreference)
              additionalParams.teacherPreference = urlParams.teacherPreference
            if (urlParams.matchLanguages)
              additionalParams.matchLanguages = urlParams.matchLanguages
            if (urlParams.dates) additionalParams.dates = urlParams.dates
            if (urlParams.time) additionalParams.time = urlParams.time
            if (urlParams.tag) additionalParams.tag = urlParams.tag

            // Redirect to new URL format
            return redirect(301, {
              path: newUrl,
              query: additionalParams,
            })
          }
        }
      }
    }

    if (!store.state.auth.passwordTokenItem) {
      await store.dispatch('teacher/getTeachers', {
        page,
        perPage: process.env.NUXT_ENV_PER_PAGE,
        params: paramsStr,
        searchQuery,
      })
    }

    // Process all params to set filters in store for UI display
    paramsStr.split(';').forEach((item) => {
      if (!item) return

      let arr = item.split(',')
      const property = arr[0]
      arr.splice(0, 1)
      arr = arr.map((item) => +item)

      if (property === 'proficiencyLevels') {
        const proficiencyLevel = filters?.proficiencyLevels.find((item) =>
          arr.includes(item.id)
        )
        if (proficiencyLevel) {
          store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
            proficiencyLevel,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'dates') {
        const dates = store.state.teacher_filter.days.filter((item) =>
          arr.includes(item.id)
        )
        store.commit('teacher_filter/SET_SELECTED_DAYS', {
          dates,
          updateActiveFilters: true,
        })
      }

      if (property === 'time') {
        const times = store.state.teacher_filter.times.filter((item) =>
          arr.includes(item.id)
        )
        store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times,
          updateActiveFilters: true,
        })
      }

      if (property === 'tag') {
        const feedbackTag = store.getters[
          'teacher_filter/feedbackTags'
        ]?.find((item) => arr.includes(item.id))
        if (feedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', feedbackTag)
        }
      }
    })

    // DON'T clear the flag here - will be cleared in mounted()

    return { filters, page }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watchQuery: true,
  mounted() {
    // Clear the loading flag after component is fully mounted
    this.$nextTick(() => {
      this.$store.commit('teacher_filter/SET_LOADING_FROM_URL', false)

      // Reprocess URL parameters on client side to ensure chips are displayed
      // This handles cases where SSR didn't fully process the parameters
      if (this.$route.query && Object.keys(this.$route.query).length > 0) {
        const {
          processUrlParameters,
        } = require('~/helpers/processUrlParameters')
        processUrlParameters(this.$route.query, this.filters, this.$store)
      }
    })
  },
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
