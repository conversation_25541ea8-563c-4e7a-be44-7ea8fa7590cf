/**
 * Language translation mappings for converting language names to i18n translation keys
 * Used across components for consistent language name translations
 */

export const LANGUAGE_TRANSLATION_MAP = {
  // Main language keys (from the root level of translation files)
  English: 'english',
  Spanish: 'spanish',
  French: 'french',
  German: 'germany', // Note: the key is 'germany' not 'german' in translation files
  Italian: 'italian',
  Portuguese: 'portuguese',
  Russian: 'russian',
  Chinese: 'chinese',
  Japanese: 'japanese',
  Korean: 'korean',
  Arabic: 'arabic',
  Dutch: 'dutch',
  Polish: 'polish',
  Swedish: 'swedish',
  Norwegian: 'norwegian',
  Danish: 'danish',
  Greek: 'greek',
  Turkish: 'turkish',
  Thai: 'thai',
  Ukrainian: 'ukrainian',
  Belarusian: 'belarusian',
  Czech: 'czech',
  Hungarian: 'hungarian',
  Bulgarian: 'bulgarian',
  Croatian: 'croatian',
  Serbian: 'serbian',
  Slovak: 'slovak',
  Welsh: 'welsh',
  Catalan: 'catalan',
  Bosnian: 'bosnian',
  Afrikaans: 'afrikaans',
  // Language names from the language_names section
  'Portuguese (Portugal)': 'portuguese-portugal',
  'Portuguese (Brazil)': 'portuguese-brazil',
  'Persian (Farsi)': 'persian-farsi',
  'Chinese (Mandarin)': 'chinese-mandarin',
  'Chinese (Cantonese)': 'chinese-cantonese',
  'Ancient Greek': 'ancient-greek',
}

/**
 * Proficiency level translation mapping
 * Maps proficiency level codes to their translation keys
 */
export const PROFICIENCY_LEVEL_MAP = {
  A1: 'A1',
  A2: 'A2',
  B1: 'B1',
  B2: 'B2',
  C1: 'C1',
  C2: 'C2',
}

/**
 * Polish language name translations (nominative case for "I also speak")
 * Maps English language names to their Polish equivalents in nominative case
 */
export const POLISH_LANGUAGE_NAMES_NOMINATIVE = {
  English: 'angielski',
  Ukrainian: 'ukraiński',
  Swedish: 'szwedzki',
  Spanish: 'hiszpański',
  Russian: 'rosyjski',
  'Portuguese (Portugal)': 'portugalski (Portugalia)',
  'Portuguese (Brazil)': 'portugalski (Brazylia)',
  Portuguese: 'portugalski',
  Polish: 'polski',
  'Persian (Farsi)': 'perski (farsi)',
  Norwegian: 'norweski',
  Japanese: 'japoński',
  Korean: 'koreański',
  Italian: 'włoski',
  Hungarian: 'węgierski',
  Greek: 'grecki',
  German: 'niemiecki',
  French: 'francuski',
  Dutch: 'niderlandzki',
  Danish: 'duński',
  Czech: 'czeski',
  Croatian: 'chorwacki',
  'Chinese (Mandarin)': 'chiński (mandaryński)',
  'Chinese (Cantonese)': 'chiński (kantoński)',
  Chinese: 'chiński',
  Catalan: 'kataloński',
  Bulgarian: 'bułgarski',
  Bosnian: 'bośniacki',
  Arabic: 'arabski',
  'Ancient Greek': 'starogrecki',
  Mathematics: 'matematyka',
  Belarusian: 'białoruski',
  Turkish: 'turecki',
  Thai: 'tajski',
  Slovak: 'słowacki',
  Welsh: 'walijski',
  Afrikaans: 'afrykanerski (Afrikaans)',
  Serbian: 'serbski',
}

/**
 * Polish language name translations
 * Maps English language names to their Polish equivalents
 */
export const POLISH_LANGUAGE_NAMES = {
  English: 'angielskiego',
  Ukrainian: 'ukraińskiego',
  Swedish: 'szwedzkiego',
  Spanish: 'hiszpańskiego',
  Russian: 'rosyjskiego',
  'Portuguese (Portugal)': 'portugalskiego (Portugalia)',
  'Portuguese (Brazil)': 'portugalskiego (Brazylia)',
  Portuguese: 'portugalskiego',
  Polish: 'polskiego',
  'Persian (Farsi)': 'perskiego (farsi)',
  Norwegian: 'norweskiego',
  Japanese: 'japońskiego',
  Korean: 'koreańskiego',
  Italian: 'włoskiego',
  Hungarian: 'węgierskiego',
  Greek: 'greckiego',
  German: 'niemieckiego',
  French: 'francuskiego',
  Dutch: 'niderlandzkiego',
  Danish: 'duńskiego',
  Czech: 'czeskiego',
  Croatian: 'chorwackiego',
  'Chinese (Mandarin)': 'chińskiego (mandaryńskiego)',
  'Chinese (Cantonese)': 'chińskiego (kantońskiego)',
  Chinese: 'chińskiego',
  Catalan: 'katalońskiego',
  Bulgarian: 'bułgarskiego',
  Bosnian: 'bośniackiego',
  Arabic: 'arabskiego',
  'Ancient Greek': 'starogreckiego',
  Mathematics: 'matematyki', // Special case for math tutoring
  Belarusian: 'białoruskiego',
  Turkish: 'tureckiego',
  Thai: 'tajskiego',
  Slovak: 'słowackiego',
  Welsh: 'walijskiego',
  Afrikaans: 'afrykanerskiego (Afrikaans)',
  Serbian: 'serbskiego',
}

/**
 * Spanish language name translations
 * Maps English language names to their Spanish equivalents
 */
export const SPANISH_LANGUAGE_NAMES = {
  English: 'inglés',
  Spanish: 'español',
  French: 'francés',
  German: 'alemán',
  Italian: 'italiano',
  Portuguese: 'portugués',
  Russian: 'ruso',
  Chinese: 'chino',
  Japanese: 'japonés',
  Korean: 'coreano',
  Arabic: 'árabe',
  Dutch: 'holandés',
  Polish: 'polaco',
  Swedish: 'sueco',
  Norwegian: 'noruego',
  Danish: 'danés',
  Greek: 'griego',
  Turkish: 'turco',
  Thai: 'tailandés',
  Ukrainian: 'ucraniano',
  Belarusian: 'bielorruso',
  Czech: 'checo',
  Hungarian: 'húngaro',
  Bulgarian: 'búlgaro',
  Croatian: 'croata',
  Serbian: 'serbio',
  Slovak: 'eslovaco',
  Welsh: 'galés',
  Afrikaans: 'afrikáans',
  Catalan: 'catalán',
  Bosnian: 'bosnio',
  'Portuguese (Portugal)': 'portugués (Portugal)',
  'Portuguese (Brazil)': 'portugués (Brasil)',
  'Persian (Farsi)': 'persa (farsi)',
  'Chinese (Mandarin)': 'chino (mandarín)',
  'Chinese (Cantonese)': 'chino (cantonés)',
  'Ancient Greek': 'griego antiguo',
}

/**
 * Converts Polish language names from nominative to genitive case
 * @param {string} languageName - The Polish language name in nominative case
 * @returns {string} - The language name in genitive case
 */
export function convertPolishToGenitive(languageName) {
  const languageGenitiveMap = {
    angielski: 'angielskiego',
    ukraiński: 'ukraińskiego',
    szwedzki: 'szwedzkiego',
    hiszpański: 'hiszpańskiego',
    rosyjski: 'rosyjskiego',
    'portugalski (Portugalia)': 'portugalskiego (Portugalia)',
    'portugalski (Brazylia)': 'portugalskiego (Brazylia)',
    portugalski: 'portugalskiego',
    polski: 'polskiego',
    'perski (farsi)': 'perskiego (farsi)',
    norweski: 'norweskiego',
    japoński: 'japońskiego',
    koreański: 'koreańskiego',
    włoski: 'włoskiego',
    węgierski: 'węgierskiego',
    grecki: 'greckiego',
    niemiecki: 'niemieckiego',
    francuski: 'francuskiego',
    holenderski: 'niderlandzkiego',
    duński: 'duńskiego',
    czeski: 'czeskiego',
    chorwacki: 'chorwackiego',
    'chiński (mandaryński)': 'chińskiego (mandaryńskiego)',
    'chiński (kantoński)': 'chińskiego (kantońskiego)',
    kataloński: 'katalońskiego',
    bułgarski: 'bułgarskiego',
    bośniacki: 'bośniackiego',
    arabski: 'arabskiego',
    starogrecki: 'starogreckiego',
    matematyka: 'matematyki',
    białoruski: 'białoruskiego',
    turecki: 'tureckiego',
    tajski: 'tajskiego',
    słowacki: 'słowackiego',
    walijski: 'walijskiego',
    'afrykanerski (Afrikaans)': 'afrykanerskiego (Afrikaans)',
    serbski: 'serbskiego',
  }

  return languageGenitiveMap[languageName] || languageName
}

/**
 * Translates a language name to the current locale using Vue i18n
 * @param {string} languageName - The language name to translate
 * @param {object} i18n - Vue i18n instance (this.$t)
 * @param {string} locale - Current locale (optional, will be detected from i18n if not provided)
 * @param {string} context - Translation context ('teach' for genitive case, 'speak' for nominative case)
 * @returns {string} - Translated language name or original name if no translation found
 */
export function translateLanguageName(
  languageName,
  i18n,
  locale = null,
  context = 'teach'
) {
  // Safety check for i18n function
  if (!i18n || typeof i18n !== 'function') {
    return languageName
  }

  // Use provided locale or default to 'en'
  const currentLocale = locale || 'en'

  // For Polish locale, use the appropriate case based on context
  if (currentLocale === 'pl') {
    if (context === 'speak' && POLISH_LANGUAGE_NAMES_NOMINATIVE[languageName]) {
      return POLISH_LANGUAGE_NAMES_NOMINATIVE[languageName]
    } else if (context === 'teach' && POLISH_LANGUAGE_NAMES[languageName]) {
      return POLISH_LANGUAGE_NAMES[languageName]
    }
  }

  // For Spanish locale, use the direct Spanish translations first
  if (currentLocale === 'es' && SPANISH_LANGUAGE_NAMES[languageName]) {
    return SPANISH_LANGUAGE_NAMES[languageName]
  }

  // Get the translation key for the language name
  const translationKey = LANGUAGE_TRANSLATION_MAP[languageName]

  // If we have a translation key, try to translate it
  if (translationKey) {
    try {
      // For Polish nominative case, try the nominative section first
      if (currentLocale === 'pl' && context === 'speak') {
        const nominativeKey = `language_names_nominative.${translationKey}`
        const nominativeTranslated = i18n(nominativeKey)
        if (nominativeTranslated && nominativeTranslated !== nominativeKey) {
          return nominativeTranslated
        }
      }

      const translated = i18n(translationKey)
      // If translation exists and is different from the key, use it
      if (translated && translated !== translationKey) {
        return translated
      }
    } catch (error) {}
  }

  const languageNamesKey = `language_names.${
    translationKey || languageName.toLowerCase()
  }`
  try {
    const languageNamesTranslated = i18n(languageNamesKey)
    if (
      languageNamesTranslated &&
      languageNamesTranslated !== languageNamesKey
    ) {
      return languageNamesTranslated
    }
  } catch (error) {}

  // Fallback to original language name if no translation found
  return languageName
}
