<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
    ></teacher-listing>

    <check-email-dialog
      :show-check-email-dialog="showCheckEmailDialog"
      @close="showCheckEmailDialog = false"
    ></check-email-dialog>

    <set-password-dialog
      :show-set-password-dialog="showSetPasswordDialog"
    ></set-password-dialog>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'
import CheckEmailDialog from '~/components/CheckEmailDialog'
import SetPasswordDialog from '~/components/SetPasswordDialog'
import { getSpecialityId, getLanguageId } from '~/helpers/urlParams'
import { processUrlParameters } from '~/helpers/processUrlParameters'

export default {
  name: 'TeacherListingPage',
  components: { TeacherListing, CheckEmailDialog, SetPasswordDialog },
  async asyncData({ store, query, req, redirect }) {
    let filters

    // Set flag to prevent navigation during initial load
    store.commit('teacher_filter/SET_LOADING_FROM_URL', true)

    // Only fetch filters if not already loaded
    if (!store.state.teacher_filter.filters) {
      await store
        .dispatch('teacher_filter/getFilters', { skipAutoSelect: true })
        .then((data) => (filters = data))
    } else {
      filters = store.state.teacher_filter.filters
    }

    // DON'T reset filters - they should persist based on URL params

    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const showCheckEmailDialog = Boolean(+query.checkEmail)
    const searchQuery = query?.search

    let params = `currency,${currentCurrency.id};sortOption,${selectedSorting.id}`

    // Process all query parameters comprehensively
    if (query) {
      processUrlParameters(query, filters, store)

      // Build params string for API call
      const appliedKeys = new Set(['currency', 'sortOption'])
      Object.keys(query).forEach((key) => {
        if (
          key !== 'search' &&
          key !== 'checkEmail' &&
          query[key] &&
          !appliedKeys.has(key)
        ) {
          params += `;${key},${query[key]}`
          appliedKeys.add(key)
        }
      })
    }

    // Check for URL parameters from new URL structure
    const urlParams = store.getters.urlParams

    if (urlParams.speciality || urlParams.languageToLearn) {
      // Get all specialities from all motivations
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      let specialityId = null
      let languageId = null

      // Try to match speciality first (if present)
      if (urlParams.speciality) {
        specialityId = getSpecialityId(urlParams.speciality, allSpecialities)

        // If no match as speciality and no second param, try as language
        if (!specialityId && !urlParams.languageToLearn) {
          languageId = getLanguageId(
            urlParams.speciality,
            filters?.languages || []
          )
          if (languageId) {
            // It's actually a language, not a speciality
            store.commit('SET_URL_PARAMS', {
              siteLanguage: urlParams.siteLanguage,
              speciality: null,
              languageToLearn: urlParams.speciality,
              additionalPath: urlParams.additionalPath,
            })
          }
        }
      }

      // Try to match language (if present)
      if (urlParams.languageToLearn) {
        languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )
      }

      if (specialityId) {
        const selectedSpeciality = allSpecialities.find(
          (spec) => spec.id === specialityId
        )
        if (selectedSpeciality) {
          store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
            specialities: [selectedSpeciality],
            updateActiveFilters: true, // Need this for chips display, but we'll prevent navigation
          })
          params += `;speciality,${specialityId}`
        }
      }

      if (languageId) {
        const selectedLanguage = filters.languages.find(
          (lang) => lang.id === languageId
        )
        if (selectedLanguage) {
          store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language: selectedLanguage,
            updateActiveFilters: true, // Need this for chips display, but we'll prevent navigation
          })
          params += `;language,${languageId}`
        }
      }
    }

    // For logged-in students, use their language preference (but don't trigger navigation)
    if (
      store.getters['user/isStudent'] &&
      !urlParams.speciality &&
      !urlParams.languageToLearn
    ) {
      const userLanguage = store.getters['user/language']
      const presetLanguage = filters?.languages.find(
        (item) => item.id === userLanguage?.id
      )

      if (presetLanguage) {
        store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
          language: presetLanguage,
          updateActiveFilters: true, // Show in chips for logged-in users
        })

        params += `;language,${presetLanguage.id}`
      }
    }

    // Redirect to new URL format if we have old URL parameters
    if (urlParams.speciality || urlParams.languageToLearn) {
      const { generateNewUrl } = require('~/helpers/urlParams')

      // Get current language and filters for URL generation
      const siteLanguage = store.state.locale || 'en'
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      // Check if we can generate a new URL
      if (urlParams.speciality && urlParams.languageToLearn) {
        const specialityId = getSpecialityId(
          urlParams.speciality,
          allSpecialities
        )
        const languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )

        if (specialityId && languageId) {
          const newUrl = generateNewUrl(
            { speciality: specialityId, language: languageId },
            siteLanguage,
            allSpecialities,
            filters?.languages || []
          )

          // Get additional parameters for query string
          const additionalParams = {}
          if (urlParams.sortOption)
            additionalParams.sortOption = urlParams.sortOption
          if (urlParams.currency) additionalParams.currency = urlParams.currency
          if (urlParams.motivation)
            additionalParams.motivation = urlParams.motivation
          if (urlParams.proficiencyLevels)
            additionalParams.proficiencyLevels = urlParams.proficiencyLevels
          if (urlParams.teacherPreference)
            additionalParams.teacherPreference = urlParams.teacherPreference
          if (urlParams.matchLanguages)
            additionalParams.matchLanguages = urlParams.matchLanguages
          if (urlParams.dates) additionalParams.dates = urlParams.dates
          if (urlParams.time) additionalParams.time = urlParams.time
          if (urlParams.tag) additionalParams.tag = urlParams.tag

          // Redirect to new URL format
          return redirect(301, {
            path: newUrl,
            query: additionalParams,
          })
        }
      } else if (urlParams.languageToLearn && !urlParams.speciality) {
        // Only language selected - redirect to /teachers/language format
        const languageId = getLanguageId(
          urlParams.languageToLearn,
          filters?.languages || []
        )

        if (languageId) {
          const language = filters?.languages?.find(
            (lang) => lang.id === languageId
          )
          if (language) {
            const languageName = language.name
              .toLowerCase()
              .replace(/\s+/g, '-')
            const baseUrl = siteLanguage === 'en' ? '' : `/${siteLanguage}`
            const newUrl = `${baseUrl}/teachers/${languageName}`

            // Get additional parameters for query string
            const additionalParams = {}
            if (urlParams.sortOption)
              additionalParams.sortOption = urlParams.sortOption
            if (urlParams.currency)
              additionalParams.currency = urlParams.currency
            if (urlParams.motivation)
              additionalParams.motivation = urlParams.motivation
            if (urlParams.proficiencyLevels)
              additionalParams.proficiencyLevels = urlParams.proficiencyLevels
            if (urlParams.teacherPreference)
              additionalParams.teacherPreference = urlParams.teacherPreference
            if (urlParams.matchLanguages)
              additionalParams.matchLanguages = urlParams.matchLanguages
            if (urlParams.dates) additionalParams.dates = urlParams.dates
            if (urlParams.time) additionalParams.time = urlParams.time
            if (urlParams.tag) additionalParams.tag = urlParams.tag

            // Redirect to new URL format
            return redirect(301, {
              path: newUrl,
              query: additionalParams,
            })
          }
        }
      }
    }

    await store.dispatch('teacher/getTeachers', {
      page: 1,
      perPage: process.env.NUXT_ENV_PER_PAGE,
      params,
      searchQuery,
    })

    if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: true,
      })
    }

    // Process all params to set filters in store for UI display
    params.split(';').forEach((item) => {
      if (!item) return

      let arr = item.split(',')
      const property = arr[0]
      arr.splice(0, 1)
      arr = arr.map((item) => +item)

      if (property === 'proficiencyLevels') {
        const proficiencyLevel = filters?.proficiencyLevels.find((item) =>
          arr.includes(item.id)
        )
        if (proficiencyLevel) {
          store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
            proficiencyLevel,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'dates') {
        const dates = store.state.teacher_filter.days.filter((item) =>
          arr.includes(item.id)
        )
        store.commit('teacher_filter/SET_SELECTED_DAYS', {
          dates,
          updateActiveFilters: true,
        })
      }

      if (property === 'time') {
        const times = store.state.teacher_filter.times.filter((item) =>
          arr.includes(item.id)
        )
        store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times,
          updateActiveFilters: true,
        })
      }

      if (property === 'tag') {
        const feedbackTag = store.getters[
          'teacher_filter/feedbackTags'
        ]?.find((item) => arr.includes(item.id))
        if (feedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', feedbackTag)
        }
      }
    })

    // DON'T clear the flag here - it will be cleared in mounted() after component is ready
    // This ensures the watcher doesn't trigger navigation during initial load

    return { filters, showCheckEmailDialog }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    isStudent() {
      return this.$store.getters['user/isStudent']
    },
    userLanguage() {
      return this.$store.getters['user/language']
    },
    showSetPasswordDialog() {
      return !!this.$store.state.auth.passwordTokenItem
    },
    currentCurrency() {
      return this.$store.state.currency.item
    },
    selectedSorting() {
      return this.$store.state.teacher_filter.selectedSorting
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watch: {
    async isUserLogged(newValue, oldValue) {
      if (newValue) {
        let params = `currency,${this.currentCurrency.id};sortOption,${this.selectedSorting.id}`

        if (this.isStudent && this.userLanguage) {
          await this.$store
            .dispatch('teacher_filter/getFilters')
            .then((data) => (this.filters = data))

          const userLanguage = this.$store.getters['user/language']
          const presetLanguage = this.filters?.languages.find(
            (item) => item.id === userLanguage?.id
          )

          if (presetLanguage) {
            this.$store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
              language: presetLanguage,
            })

            params += `;language,${presetLanguage.id}`
          }
        }

        await this.$store.dispatch('teacher/getTeachers', {
          page: 1,
          perPage: process.env.NUXT_ENV_PER_PAGE,
          params,
        })
        await this.$store.dispatch('teacher_filter/updateCurrencyActiveFilter')
      }
    },
  },
  watchQuery: true,
  mounted() {
    // Clear the loading flag after component is fully mounted
    // This prevents the needUpdateTeachers watcher from triggering navigation
    this.$nextTick(() => {
      this.$store.commit('teacher_filter/SET_LOADING_FROM_URL', false)

      // Reprocess URL parameters on client side to ensure chips are displayed
      // This handles cases where SSR didn't fully process the parameters
      if (this.$route.query && Object.keys(this.$route.query).length > 0) {
        const {
          processUrlParameters,
        } = require('~/helpers/processUrlParameters')
        processUrlParameters(this.$route.query, this.filters, this.$store)
      }
    })
  },
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
