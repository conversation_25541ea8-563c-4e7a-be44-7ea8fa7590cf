// Nuxt server middleware for Whereby API
const https = require('https')

// Whereby API configuration
const WHEREBY_TOKEN =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.1dZgX4paCsT4jgcW7ueteClMW8ZzlASc8j1LphFja8U'
const WHEREBY_API_BASE = 'https://api.whereby.dev/v1'

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise(function (resolve, reject) {
    let body = ''
    req.on('data', function (chunk) {
      body += chunk.toString()
    })
    req.on('end', function () {
      try {
        resolve(body ? JSON.parse(body) : {})
      } catch (error) {
        reject(error)
      }
    })
  })
}

// Helper function to make HTTP requests
function makeRequest(url, options) {
  if (!options) options = {}

  return new Promise(function (resolve, reject) {
    // Use url.parse for Node.js 10 compatibility
    const urlObj = require('url').parse(url)
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.path,
      method: options.method || 'GET',
      headers: Object.assign(
        {
          'Content-Type': 'application/json',
        },
        options.headers || {}
      ),
    }

    const req = https.request(requestOptions, function (res) {
      let data = ''
      res.on('data', function (chunk) {
        data += chunk
      })
      res.on('end', function () {
        try {
          const response = {
            status: res.statusCode,
            data: data ? JSON.parse(data) : null,
          }
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(response)
          } else {
            reject(new Error('HTTP ' + res.statusCode + ': ' + data))
          }
        } catch (error) {
          reject(
            new Error(
              'JSON Parse Error: ' + error.message + '. Raw response: ' + data
            )
          )
        }
      })
    })

    req.on('error', reject)

    if (options.data) {
      req.write(JSON.stringify(options.data))
    }

    req.end()
  })
}

// Main middleware function
module.exports = async function (req, res) {
  try {
	
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      res.statusCode = 200
      res.end()
      return
    }

    // Route handling
    if (req.url === '/create-room' && req.method === 'POST') {
      await createRoom(req, res)
    } else if (req.url.startsWith('/room/') && req.method === 'GET') {
      getRoomInfo(req, res)
    } else if (req.url.startsWith('/room/') && req.method === 'DELETE') {
      await deleteRoom(req, res)
    } else if (req.url === '/health' && req.method === 'GET') {
      res.statusCode = 200
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          status: 'ok',
          service: 'whereby-api',
          timestamp: new Date().toISOString(),
        })
      )
    } else {
      res.statusCode = 404
      res.setHeader('Content-Type', 'application/json')
      res.end(JSON.stringify({ error: 'Not found' }))
    }
  } catch (error) {
    console.error('[Whereby API] Unhandled error:', error)
    res.statusCode = 500
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message,
      })
    )
  }
}

// Create a new Whereby room for a classroom session
async function createRoom(req, res) {
  try {
    const body = await parseBody(req)
    const { lessonId } = body

    if (!lessonId) {
      res.statusCode = 400
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          error: 'lessonId is required',
        })
      )
      return
    }

    // Calculate room duration (default 2 hours for lessons)
    const endDate = new Date(Date.now() + 2 * 60 * 60 * 1000) // 2 hours from now

    // Whereby room creation payload
    const roomData = {
      endDate: endDate.toISOString(),
      roomMode: 'group',
      isLocked: false,
      roomNamePrefix: 'langu-lesson',
      roomNamePattern: 'uuid',
      fields: ['hostRoomUrl', 'roomUrl', 'meetingId'],
    }

    // Create room via Whereby API
    const response = await makeRequest(`${WHEREBY_API_BASE}/meetings`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${WHEREBY_TOKEN}`,
        'Content-Type': 'application/json',
      },
      data: roomData,
    })

    const roomInfo = response.data

    // Return room information
    res.statusCode = 200
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        success: true,
        room: {
          lessonId,
          meetingId: roomInfo.meetingId,
          hostRoomUrl: roomInfo.hostRoomUrl, // For teachers
          roomUrl: roomInfo.roomUrl, // For students
          startDate: roomInfo.startDate,
          endDate: roomInfo.endDate,
          createdAt: new Date().toISOString(),
        },
      })
    )
  } catch (error) {
    console.error(
      'Error creating Whereby room:',
      (error.response && error.response.data) || error.message
    )

    // Return error response
    res.statusCode = 500
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        error: 'Failed to create Whereby room',
        details:
          (error.response &&
            error.response.data &&
            error.response.data.error) ||
          error.message,
      })
    )
  }
}

// Get room information
function getRoomInfo(req, res) {
  try {
    const urlParts = req.url.split('/')
    const lessonId = urlParts[urlParts.length - 1]

    if (!lessonId) {
      res.statusCode = 400
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          error: 'lessonId is required',
        })
      )
      return
    }

    // In a real implementation, you would fetch this from your database
    // For now, we'll return a placeholder response
    res.statusCode = 200
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        success: true,
        message: 'Room info endpoint - implement database lookup',
        lessonId,
      })
    )
  } catch (error) {
    console.error('Error fetching room info:', error.message)
    res.statusCode = 500
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        error: 'Failed to fetch room information',
        details: error.message,
      })
    )
  }
}

// Delete/end a room
async function deleteRoom(req, res) {
  try {
    const urlParts = req.url.split('/')
    const meetingId = urlParts[urlParts.length - 1]

    if (!meetingId) {
      res.statusCode = 400
      res.setHeader('Content-Type', 'application/json')
      res.end(
        JSON.stringify({
          error: 'meetingId is required',
        })
      )
      return
    }

    // End the meeting via Whereby API
    await makeRequest(`${WHEREBY_API_BASE}/meetings/${meetingId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${WHEREBY_TOKEN}`,
      },
    })

    res.statusCode = 200
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        success: true,
        message: 'Room ended successfully',
        meetingId,
      })
    )
  } catch (error) {
    console.error(
      'Error ending Whereby room:',
      (error.response && error.response.data) || error.message
    )
    res.statusCode = 500
    res.setHeader('Content-Type', 'application/json')
    res.end(
      JSON.stringify({
        error: 'Failed to end room',
        details:
          (error.response &&
            error.response.data &&
            error.response.data.error) ||
          error.message,
      })
    )
  }
}

// Health check is handled in the main middleware function above
