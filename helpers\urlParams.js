// Helper functions for converting URL parameters to filter IDs

/**
 * Convert speciality name to ID
 * @param {string} specialityName - The speciality name from URL (e.g., 'business')
 * @param {Array} specialities - Array of speciality objects
 * @returns {number|null} - The speciality ID or null if not found
 */
export function getSpecialityId(specialityName, specialities) {
  if (!specialityName || !specialities) return null

  const specialitySlug = specialityName.toLowerCase().trim()

  // First try exact match with hyphen replacement
  let speciality = specialities.find((spec) => {
    const specName = spec.name.toLowerCase().trim().replace(/\s+/g, '-')
    return specName === specialitySlug
  })

  if (speciality) return speciality.id

  // Try exact match without transformation (in case name already has hyphens)
  speciality = specialities.find((spec) => {
    const specName = spec.name.toLowerCase().trim()
    return (
      specName === specialitySlug ||
      specName.replace(/\s+/g, '-') === specialitySlug
    )
  })

  if (speciality) return speciality.id

  // Try matching without hyphens/spaces for "generallearning" vs "general learning"
  const normalizedSlug = specialitySlug.replace(/[-\s]+/g, '')
  speciality = specialities.find((spec) => {
    const normalizedSpecName = spec.name
      .toLowerCase()
      .trim()
      .replace(/[-\s]+/g, '')
    return normalizedSpecName === normalizedSlug
  })

  return speciality ? speciality.id : null
}

/**
 * Convert language name to ID
 * @param {string} languageName - The language name from URL (e.g., 'english')
 * @param {Array} languages - Array of language objects
 * @returns {number|null} - The language ID or null if not found
 */
export function getLanguageId(languageName, languages) {
  if (!languageName || !languages) return null

  const languageSlug = languageName.toLowerCase().trim()

  // First try exact match with hyphen replacement
  let language = languages.find((lang) => {
    const langName = lang.name.toLowerCase().trim().replace(/\s+/g, '-')
    return langName === languageSlug
  })

  if (language) return language.id

  // Try exact match without transformation
  language = languages.find((lang) => {
    const langName = lang.name.toLowerCase().trim()
    return (
      langName === languageSlug ||
      langName.replace(/\s+/g, '-') === languageSlug
    )
  })

  if (language) return language.id

  // Try matching without hyphens/spaces
  const normalizedSlug = languageSlug.replace(/[-\s]+/g, '')
  language = languages.find((lang) => {
    const normalizedLangName = lang.name
      .toLowerCase()
      .trim()
      .replace(/[-\s]+/g, '')
    return normalizedLangName === normalizedSlug
  })

  return language ? language.id : null
}

/**
 * Convert speciality ID to URL-friendly name
 * @param {number} specialityId - The speciality ID
 * @param {Array} specialities - Array of speciality objects
 * @returns {string|null} - The URL-friendly name or null if not found
 */
export function getSpecialityUrlName(specialityId, specialities) {
  if (!specialityId || !specialities) return null

  const speciality = specialities.find((spec) => spec.id === specialityId)

  return speciality ? speciality.name.toLowerCase().replace(/\s+/g, '-') : null
}

/**
 * Convert language ID to URL-friendly name
 * @param {number} languageId - The language ID
 * @param {Array} languages - Array of language objects
 * @returns {string|null} - The URL-friendly name or null if not found
 */
export function getLanguageUrlName(languageId, languages) {
  if (!languageId || !languages) return null

  const language = languages.find((lang) => lang.id === languageId)

  return language ? language.name.toLowerCase().replace(/\s+/g, '-') : null
}

/**
 * Generate new URL structure from filter parameters
 * @param {Object} params - Filter parameters
 * @param {string} siteLanguage - Site language (pl, es, en)
 * @param {Array} specialities - Array of speciality objects
 * @param {Array} languages - Array of language objects
 * @returns {string} - The new URL
 */
export function generateNewUrl(params, siteLanguage, specialities, languages) {
  const { speciality, language } = params

  // Build base URL - only add language prefix for pl and es
  const baseUrl =
    siteLanguage && siteLanguage !== 'en' ? `/${siteLanguage}` : ''

  if (!speciality || !language) {
    return `${baseUrl}/teachers`
  }

  const specialityName = getSpecialityUrlName(speciality, specialities)
  const languageName = getLanguageUrlName(language, languages)

  if (!specialityName || !languageName) {
    return `${baseUrl}/teachers`
  }

  return `${baseUrl}/teachers/${specialityName}-${languageName}`
}

// Common speciality and language mappings for fallback
export const SPECIALITY_MAPPINGS = {
  business: 'business',
  conversation: 'conversation',
  'exam-preparation': 'exam-preparation',
  general: 'general',
  grammar: 'grammar',
  pronunciation: 'pronunciation',
  travel: 'travel',
  writing: 'writing',
}

export const LANGUAGE_MAPPINGS = {
  english: 'english',
  spanish: 'spanish',
  polish: 'polish',
  french: 'french',
  german: 'german',
  italian: 'italian',
  portuguese: 'portuguese',
  russian: 'russian',
}
