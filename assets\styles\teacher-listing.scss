@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.teacher-listing {
  max-width: 1430px;
  margin: 0 auto;
  padding: 20px 0 45px;

  &-wrap {
    display: flex;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      flex-direction: column;
    }
  }

  &-content {
    width: calc(100% - 345px);
    @media #{map-get($display-breakpoints, 'md-only')} {
      width: calc(100% - 280px);
      padding-left: 15px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;
      padding-left: 0;
    }
  }

  &-result {
    &-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: stretch;
      width: 100%;
      margin-top: 18px;

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        width: calc(100% + 15px);
        margin-bottom: 15px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        margin-top: 28px;
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        flex-direction: column;
        width: 100%;
      }
    }

    &-item {
      width: 50%;
      padding-bottom: 25px;

      // Alternating padding pattern: odd items get right padding, even items don't
      &:nth-child(odd) {
        padding-right: 20px;
      }

      &:nth-child(even) {
        padding-right: 0;
      }

      @media #{map-get($display-breakpoints, 'md-and-down')} {
        &:nth-child(odd) {
          padding-right: 15px;
        }
      }
      @media screen and (min-width: 767px) and (max-width: 860px) {
          &:nth-child(even) {
          padding-right: 15px;
        }
      }

      @media #{map-get($display-breakpoints, 'xs-only')} {
        width: 100%;
        padding: 0 0 24px 0;
        &:nth-child(odd) {
          padding-right: 0px;
        }

        .teacher-card {
          margin: 0 auto;
        }
      }
    }

    &--empty {
      max-width: 600px;
      margin: 0 auto;
      padding: 60px 15px 0;
      font-size: 15px;

      a {
        font-weight: 700;
        text-decoration: none;
        color: var(--v-orange-base);
        transition: color 0.3s;

        &:hover {
          color: var(--v-dark-base);
        }
      }
    }
  }

  .filters {
    width: 345px;
    color: #fff;

    @media #{map-get($display-breakpoints, 'md-and-up')} {
      & > form {
        padding: 24px 0 104px;
        background-color: var(--v-darkLight-base);
        border-radius: 30px;
      }
    }

    @media #{map-get($display-breakpoints, 'md-only')} {
      width: 280px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      width: 100%;

      & > form {
        padding: 78px 0 46px;
      }
    }

    @media only screen and (max-width: $xxs-and-down) {
      width: 100%;
    }

    &-head {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding: 0 24px 22px;
      font-weight: 700;

      @media #{map-get($display-breakpoints, 'md-only')} {
        padding: 0 15px 22px;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        padding: 0 18px 22px;
        border-bottom: 1px solid rgba(255,255,255,.1);

        & > div {
          width: 33.3333%;
        }
      }

      &-title {
        padding-right: 10px;
        font-size: 24px;
        line-height: 1.33;

        @media #{map-get($display-breakpoints, 'md-only')} {
          font-size: 18px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          padding-right: 0;
          text-align: center;
        }
      }

      &-clear {
        font-size: 14px;
        color: var(--v-orange-base);
        letter-spacing: 0.3px;
        cursor: pointer;
        transition: color 0.3s;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
          white-space: nowrap;
        }

        @media #{map-get($display-breakpoints, 'sm-and-down')} {
          font-size: 18px;
          line-height: 1.1;
        }

        &:hover {
          color: #fff;
        }
      }

      &-close {
        text-align: right;
        line-height: 0;

        &-icon {
          display: inline-block;
          width: 34px;
          height: 34px;
          cursor: pointer;
        }
      }
    }

    &-content {
      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 0 18px;
      }

      .v-expansion-panel {
        margin: 0 !important;
        padding: 24px 0 25px !important;
        background-color: var(--v-darkLight-base) !important;
        border-radius: 0 !important;

        &::after {
          content: '';
          position: absolute;
          width: calc(100% - 48px);
          height: 1px;
          left: 24px;
          top: auto;
          bottom: 0;
          background-color: rgba(255,255,255,.1);
          border: none !important;

          @media #{map-get($display-breakpoints, 'md-and-down')} {
            width: calc(100% - 30px);
            left: 15px;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            width: 100%;
            left: 0;
          }
        }

        &:last-child::after {
          display: none;
        }

        &-header {
          min-height: 28px !important;
          padding: 0 24px !important;
          color: #fff !important;
          font-weight: 600;
          line-height: 1.556;
          font-size: 18px;
          text-transform: uppercase;
          opacity: 1 !important;

          @media #{map-get($display-breakpoints, 'md-only')} {
            padding: 0 15px !important;
            font-size: 15px;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 0 !important;
          }

          &--active > div {
            color: var(--v-success-base);
            background: linear-gradient(-75deg, var(--v-success-base), var(--v-primary-base));
            background: -moz-linear-gradient(-75deg, var(--v-success-base), var(--v-primary-base));
            background: -webkit-linear-gradient(-75deg, var(--v-success-base), var(--v-primary-base));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        &-content__wrap {
          padding: 16px 0 0 24px !important;
          color: #fff !important;
          font-size: 16px;

          @media #{map-get($display-breakpoints, 'md-only')} {
            padding: 16px 0 0 15px !important;
          }

          @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 16px 0 0 !important;
          }

          .checkbox {
            margin: 0 5px 16px 0;

            .v-input {
              .v-label {
                line-height: 1.2 !important;

                @media #{map-get($display-breakpoints, 'md-only')} {
                  font-size: 14px !important;
                }
              }

              .v-input--selection-controls__input {
                width: 16px;
                height: 16px;
                margin-top: 2px;
                margin-right: 10px;

                @media #{map-get($display-breakpoints, 'md-and-down')} {
                  margin-top: 0;
                }

                @media #{map-get($display-breakpoints, 'sm-and-down')} {
                  margin-top: 2px;
                }
              }
            }

            &--motivation {
              display: flex;
              align-items: center;
              margin-bottom: 0;
              color: #fff;

              .checkbox-icon {
                display: flex;
                align-items: center;
                margin-right: 7px;
              }

              .v-input--selection-controls__input {
                display: none !important;
              }
            }

            &--checked {
              .checkbox-icon svg {
                color: var(--v-success-base);
                transition: color 0.3s;
              }

              label {
                color: var(--v-success-base);
                transition: color 0.3s;
              }
            }

            &-period {
              font-size: 14px;
              opacity: 0.4;
            }
          }

          .l-radio-button:not(:last-child) {
            margin-bottom: 16px;
          }

          .autocomplete {
            @media #{map-get($display-breakpoints, 'md-and-up')} {
              padding-right: 16px;
            }

            .v-input__icon--append .v-icon {
              color: var(--v-orange-base) !important;
            }

            .v-input {
              &:not(.v-input--is-focused) {
                .v-select__slot > * {
                  cursor: pointer !important;
                }
              }
            }

            .v-text-field.v-text-field--enclosed:not(.v-text-field--rounded) > .v-input__control > .v-input__slot, .v-text-field.v-text-field--enclosed .v-text-field__details {
              padding: 0 !important;
            }
          }
        }
      }
    }

    &-bottom {
      padding: 60px 24px 0;

      @media #{map-get($display-breakpoints, 'md-only')} {
        padding: 60px 15px 0;
      }

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 40px 18px 0;
      }
    }

    &-notice {
      padding-right: 16px;
      color: rgba(255, 255, 255, 0.4);
    }

    .chips {
      display: flex;
      flex-wrap: wrap;

      @media #{map-get($display-breakpoints, 'md-and-up')} {
        padding: 0 16px;
      }

      .chip {
        margin: 0 16px 0 0;
      }
    }
  }

  .filter-button {
    position: relative;
    height: 60px;
    margin-bottom: 32px;
    padding: 10px 70px 10px 16px;
    background-color: var(--v-darkLight-base);
    color: #fff;
    border-radius: 16px;
    line-height: 40px;
    font-weight: 600;
    font-size: 22px;
    letter-spacing: 0.1px;
    cursor: pointer;

    span {
      color: #5C9D90;
      background: linear-gradient(-70deg, #5C9D90, #468ED8);
      background: -moz-linear-gradient(-70deg, #5C9D90, #468ED8);
      background: -webkit-linear-gradient(-70deg, #5C9D90, #468ED8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &-icon {
      position: absolute;
      right: 24px;
      top: 50%;
      width: 24px;
      height: 30px;
      transform: translateY(-50%);
    }
  }
  .teacher-card-rating {
    .score {
      color: #000 !important;
    }
  }
  .v-radio {
    align-items: center !important;
  }
}

.filters-dropdown-list {
  position: relative !important;
  top: auto !important;
  box-shadow: none !important;

  .v-list {
    padding: 0 !important;
    background-color: var(--v-darkLight-base) !important;

    &-item {
      position: relative;
      min-height: 32px !important;
      padding: 0 5px 0 0;

      &:hover,
      &:focus {
        &::before {
          display: none;
        }
      }

      &__title {
        font-size: 16px !important;
        font-weight: 400 !important;
        letter-spacing: 0.3px;
        transition: color 0.3s;

        &:hover {
          color: var(--v-success-base);
        }
      }

      &__mask {
        color: #fff !important;
        background: var(--v-orangeLight-base) !important;
      }

      .icon {
        position: absolute;
        border-radius: 50%;
        overflow: hidden;
      }

      .text {
        padding-left: 38px;
      }
    }
  }
}

.teacher-filters {
  @media only screen and (max-width: $xxs-and-down) {
    width: 100% !important;
  }
}

.es {
  .teacher-listing .filters-head-clear {
    font-size: 16px;
  }
}
