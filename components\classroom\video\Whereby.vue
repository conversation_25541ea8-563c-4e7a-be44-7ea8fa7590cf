<template>
  <div class="whereby-stream">
    <classroom-container
      :asset="file"
      :hover-enabled="true"
      :lock-aspect-ratio="false"
    >
      <div
        id="video-window"
        :class="[
          'whereby-component cursor-before-grab',
          { 'video-window--is-fullscreen': settings.isFullscreenEnabled },
        ]"
      >
        <!-- Draggable header area for better drag/resize interaction -->
        <div class="whereby-header cursor-before-grab">
          <div class="whereby-header-title"></div>
        </div>
        <div id="whereby-video-container" class="whereby-video-container"></div>

        <!-- Whereby is now the default and only video provider -->
        <!-- Video provider switcher removed as requested -->
        <!--
        <div class="stream-controls">
          <div id="video-window-buttons" class="video-window-buttons-wrap">
            <div class="stream-controls-wrapper cursor-auto">
              <div class="d-flex align-center">
                <div>{{ $t('video') }}:</div>
                <div class="d-inline-flex ml-1">
                  <div class="toolbar-button-wrapper">
                    <v-btn
                      :class="[
                        'toolbar-button-item cursor-pointer pa-0',
                        {
                          'toolbar-button-item--selected':
                            file.asset.type === 'twilio',
                        },
                      ]"
                      width="18"
                      height="18"
                      text
                      @click="switchVideoPlayer('twilio')"
                    >
                      A
                    </v-btn>
                  </div>
                  <div class="toolbar-button-wrapper">
                    <v-btn
                      :class="[
                        'toolbar-button-item cursor-pointer pa-0',
                        {
                          'toolbar-button-item--selected':
                            file.asset.type === 'tokbox',
                        },
                      ]"
                      width="18"
                      height="18"
                      text
                      @click="switchVideoPlayer('tokbox')"
                    >
                      B
                    </v-btn>
                  </div>
                  <div class="toolbar-button-wrapper">
                    <v-btn
                      :class="[
                        'toolbar-button-item cursor-pointer pa-0',
                        {
                          'toolbar-button-item--selected':
                            file.asset.type === 'whereby',
                        },
                      ]"
                      width="18"
                      height="18"
                      text
                      @click="switchVideoPlayer('whereby')"
                    >
                      C
                    </v-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        -->
      </div>
    </classroom-container>

    <classroom-container
      v-show="isLocalScreenShareEnabled || isRemoteScreenShareEnabled"
      :asset="screenShareAsset"
      :hover-enabled="true"
    >
      <div class="whereby-component screenshare-component cursor-before-grab">
        <div class="user-name">
          <template v-if="isLocalScreenShareEnabled">
            {{ $t('my_screen') }}
          </template>
          <template v-if="isRemoteScreenShareEnabled">
            {{
              $t('classroom_user_screen', {
                username: zoomOtherAsset.asset.username,
              })
            }}
          </template>
        </div>
        <div
          id="whereby-screenshare-placeholder"
          class="screenshare-stream"
        ></div>
      </div>
    </classroom-container>
  </div>
</template>

<script>
import { isSupportedScreenShare } from '~/helpers/check_device'
import { createWherebyRoom } from '~/helpers/whereby-api'
import { switchFullScreen } from '~/helpers'

import ClassroomContainer from '~/components/classroom/ClassroomContainer'

export default {
  name: 'Whereby',
  components: {
    ClassroomContainer,
  },
  props: {
    file: {
      type: Object,
      required: true,
    },
    screenShareAsset: {
      type: Object,
      required: true,
    },
    zoomOtherAsset: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    localStreamContainer: null,
    screenShareStreamContainer: null,
    isLocalScreenShareEnabled: false,
    isRemoteScreenShareEnabled: false,
    screenSharingNotSupported: !isSupportedScreenShare(),
    isJoined: false,
    currentRole: 'participant', // Will be determined based on classroom role
    wherebyRoom: null, // Store the created room information
    isCreatingRoom: false, // Loading state for room creation
    settings: {
      isFullscreenEnabled: false,
      isScreenShareEnabled: false,
    },
    // Properties for Whereby features (not used in UI but may be referenced)
    isHandRaised: false,
    isChatEnabled: false,
    isParticipantsEnabled: false,
  }),
  computed: {
    lessonId() {
      return this.file.lessonId || this.$store.state.classroom.lessonId
    },
    teacherId() {
      return this.$store.state.classroom.teacherId
    },
    studentId() {
      return this.$store.state.classroom.studentId
    },
    role() {
      return this.$store.getters['classroom/role']
    },
    isVideoEnabled() {
      return this.file.asset?.settings?.[this.role]?.isVideoEnabled ?? true
    },
    isMuted() {
      return this.file.asset?.settings?.[this.role]?.isMuted ?? false
    },
    maxIndex() {
      return this.$store.state.classroom.maxIndex
    },
  },
  mounted() {
    this.$nextTick(() => {
      // Add debugging to check if elements exist
      this.localStreamContainer = document.getElementById(
        'whereby-video-container'
      )
      this.screenShareStreamContainer = document.getElementById(
        'whereby-screenshare-placeholder'
      )
      // Only initialize if container exists
      if (this.localStreamContainer) {
        this.initializeWhereby()
      } else {
        setTimeout(() => {
          this.localStreamContainer = document.getElementById(
            'whereby-video-container'
          )
          if (this.localStreamContainer) {
            this.initializeWhereby()
          }
        }, 500)
      }
    })

    window.addEventListener('beforeunload', this.closeStream)
    window.addEventListener('pagehide', this.closeStream)

    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener(
      'fullscreenchange',
      this.fullscreenChangeHandler
    )
    this.closeStream()
  },
  methods: {
    async initializeWhereby() {
      try {
        // Check if container exists
        if (!this.localStreamContainer) {
          return
        }

        // Determine role based on classroom role
        const isTeacher = this.role === 'teacher'
        this.currentRole = isTeacher ? 'host' : 'participant'

        // Show optimized loading state with better UX
        this.isCreatingRoom = true
        this.showOptimizedLoadingState()
        // Create or get existing room with optimized caching
        if (!this.wherebyRoom) {
          try {
            // Dynamic room creation with retry logic and caching
            this.wherebyRoom = await createWherebyRoom({
              lessonId: this.lessonId,
              teacherId: this.teacherId,
              studentId: this.studentId,
              isRecurring: false,
            })
          } catch (error) {
            this.showErrorState(error.message)
            return
          }
        }

        // Hide loading state
        this.isCreatingRoom = false

        // Create iframe for inline embedding like options A and B
        const iframe = document.createElement('iframe')

        // Determine the URL based on role
        const baseUrl =
          this.currentRole === 'host'
            ? this.wherebyRoom.hostRoomUrl
            : this.wherebyRoom.roomUrl

        // Build embed parameters - explicitly disable requested features
        const embedParams = new URLSearchParams({
          embed: '',
          displayName: this.$store.getters['classroom/userName'] || 'User',
          audio: !this.isMuted ? 'on' : 'off',
          video: this.isVideoEnabled ? 'on' : 'off',
          // Explicitly disable requested features
          chat: 'off',
          people: 'off',
          screenshare: 'on',
          reactions: 'on',
          handRaise: 'on',
          leaveButton: 'off',
          background: 'on',
          recording: 'off',
          breakoutRooms: 'on',
          whiteboard: 'on',
          minimal: 'false',
          // Direct entry without knocking
          skipMediaPermissionPrompt: 'true',
          autoJoin: 'true',
        })

        // Determine if we need to add & or ? for parameters
        const separator = baseUrl.includes('?') ? '&' : '?'
        iframe.src = `${baseUrl}${separator}${embedParams.toString()}`

        iframe.style.width = '100%'
        iframe.style.height = '100%'
        iframe.style.border = 'none'
        iframe.style.borderRadius = '8px'
        iframe.style.pointerEvents = 'auto' // Allow iframe to receive events when not being dragged
        iframe.allow =
          'camera; microphone; fullscreen; display-capture; autoplay'
        iframe.allowFullscreen = true

        // Clear container and add iframe
        this.localStreamContainer.innerHTML = ''
        this.localStreamContainer.appendChild(iframe)

        // No need for complex iframe event handling since we have a header for dragging

        // Set up message listener for iframe communication
        window.addEventListener('message', this.handleWherebyMessage)

        // Mark as joined after a short delay
        setTimeout(() => {
          this.isJoined = true
        }, 1000)
      } catch (error) {
        this.isCreatingRoom = false

        // Check if container exists before setting innerHTML
        if (this.localStreamContainer) {
          this.localStreamContainer.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px;">
              <div style="margin-bottom: 20px;">
                <h3 style="color: #d32f2f; margin-bottom: 10px;">Failed to Create Room</h3>
                <p style="color: #666; margin-bottom: 20px;">Unable to create Whereby room. Please try switching to another video provider.</p>
              </div>
              <button
                onclick="this.parentElement.parentElement.parentElement.querySelector('.toolbar-button-item').click()"
                style="
                  background: #5E72E4;
                  color: white;
                  border: none;
                  padding: 12px 20px;
                  border-radius: 6px;
                  font-size: 14px;
                  cursor: pointer;
                  font-weight: 600;
                "
              >
                Switch to Provider A
              </button>
            </div>
          `
        }
      }
    },

    handleWherebyMessage(event) {
      // Handle messages from Whereby iframe
      if (!event.origin.includes('whereby.com')) {
        return
      }

      try {
        const data = JSON.parse(event.data)

        switch (data.type) {
          case 'participant_join':
            break
          case 'participant_leave':
            break
          case 'screenshare_start':
            this.isRemoteScreenShareEnabled = true
            break
          case 'screenshare_stop':
            this.isRemoteScreenShareEnabled = false
            break
          case 'error':
            this.handleMediaError(data.error)
            break
        }
      } catch (e) {
        // Ignore non-JSON messages
      }
    },
    handleMediaError(error) {
      alert(
        error &&
          'Could not connect to video call. Please check your camera and microphone permissions.'
      )
    },
    toggleVideo() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: !this.isVideoEnabled,
            isMuted: this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
    },
    toggleAudio() {
      this.updateData(this.file.id, {
        settings: {
          ...this.file.asset.settings,
          [this.role]: {
            isVideoEnabled: this.isVideoEnabled,
            isMuted: !this.isMuted,
          },
        },
      })

      // Note: With iframe approach, video/audio controls are handled within the Whereby interface
      // The iframe will respect the initial settings passed in the URL parameters
    },
    toggleFullScreen() {
      this.settings.isFullscreenEnabled = !this.settings.isFullscreenEnabled
      switchFullScreen(this.settings.isFullscreenEnabled)
    },

    toggleScreenShare() {
      this.settings.isScreenShareEnabled = !this.settings.isScreenShareEnabled

      if (this.settings.isScreenShareEnabled) {
        this.isLocalScreenShareEnabled = false
        this.screenShareStreamContainer.innerHTML = ''
      } else {
        // Note: With iframe approach, screen sharing is handled within the Whereby interface
        // Users can use the screen share button within the Whereby room
        this.updateData(this.screenShareAsset.id, {
          index: this.maxIndex + 1,
        })
        this.isLocalScreenShareEnabled = true
      }
    },

    toggleHandRaise() {
      this.isHandRaised = !this.isHandRaised
      // Note: Actual hand raising is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleChat() {
      this.isChatEnabled = !this.isChatEnabled
      // Note: Actual chat toggle is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },

    toggleParticipants() {
      this.isParticipantsEnabled = !this.isParticipantsEnabled
      // Note: Actual participants panel is handled by Whereby iframe
      // This just updates the UI state for the external controls
    },
    // Video player switching disabled - Whereby is now the only option
    /*
    switchVideoPlayer(type) {
      this.$store.dispatch('classroom/deleteAsset', this.file)
      this.$store.dispatch('classroom/createAsset', {
        ...this.file.asset,
        type,
      })
    },
    */
    fullscreenChangeHandler() {
      if (!document.fullscreenElement) {
        this.settings.isFullscreenEnabled = false
      }
    },
    showOptimizedLoadingState() {
      if (!this.localStreamContainer) return

      this.localStreamContainer.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); text-align: center; padding: 20px; border-radius: 8px;">
          <div style="margin-bottom: 20px;">
            <h3 style="color: white; margin-bottom: 10px; font-weight: 600;">Connecting to Video Call...</h3>
            <p style="color: rgba(255,255,255,0.8); margin-bottom: 20px;">Setting up your Whereby room</p>
          </div>
          <div style="width: 40px; height: 40px; border: 3px solid rgba(255,255,255,0.3); border-top: 3px solid white; border-radius: 50%; animation: spin 1s linear infinite;"></div>
          <div style="margin-top: 15px; color: rgba(255,255,255,0.7); font-size: 12px;">This usually takes just a few seconds</div>
          <style>
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </div>
      `
    },
    showErrorState(errorMessage) {
      if (!this.localStreamContainer) return

      this.localStreamContainer.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; text-align: center; padding: 20px; border-radius: 8px; border: 2px dashed #e0e0e0;">
          <div style="margin-bottom: 20px;">
            <div style="width: 60px; height: 60px; background: #ff5252; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
              <span style="color: white; font-size: 24px;">⚠</span>
            </div>
            <h3 style="color: #d32f2f; margin-bottom: 10px;">Connection Failed</h3>
            <p style="color: #666; margin-bottom: 20px; max-width: 300px;">Unable to create video room. Please try refreshing the page.</p>
            <p style="color: #999; font-size: 12px; margin-bottom: 20px;">${errorMessage}</p>
          </div>
          <button
            onclick="window.location.reload()"
            style="background: #5E72E4; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-weight: 500;">
            Refresh Page
          </button>
        </div>
      `
    },
    updateData(id, asset) {
      this.$store.commit('classroom/moveAsset', {
        id,
        asset,
      })
      this.$store.dispatch('classroom/moveAsset', {
        id,
        lessonId: this.file.lessonId,
        asset,
      })
    },
    closeStream() {
      // Clean up iframe and event listeners
      if (this.localStreamContainer) {
        this.localStreamContainer.innerHTML = ''
      }
      window.removeEventListener('message', this.handleWherebyMessage)
    },
  },
}
</script>

<style lang="scss" scoped>
.whereby-stream {
  .whereby-component {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;

    .whereby-header {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      padding: 0 12px;
      z-index: 10;
      border-radius: 8px 8px 0 0;

      .whereby-header-title {
        color: white;
        font-size: 12px;
        font-weight: 500;
        user-select: none;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.8);
      }
    }

    .whereby-video-container {
      width: 100%;
      height: 100%;
      min-height: 200px;
    }

    &.screenshare-component {
      .user-name {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 10;
      }

      .screenshare-stream {
        width: 100%;
        height: 100%;
        min-height: 200px;
      }
    }

    &.video-window--is-fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999;
      border-radius: 0;
    }
  }
}
</style>
