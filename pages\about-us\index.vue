<template>
  <v-row>
    <v-col class="col-12 px-0">
      <div class="about-us-page-container">
        <div
          class="heading d-flex justify-end flex-column mx-auto text-center"
          :style="{
            'background-image':
              'url(' +
              require('~/assets/images/about-us-page/aga-and-travis-cambridge-offsite.jpg') +
              ')',
          }"
        >
          <h1
            class="heading-text font-weight-regular white--text text-center"
            v-html="$t('about_us_page.title')"
          ></h1>
        </div>
        <v-container fluid class="py-0">
          <v-row>
            <v-col class="col-12">
              <div class="about-us-page-content mx-auto">
                <div class="about-us-page-intro mx-auto text-center">
                  {{ $t('about_us_page.intro') }}
                </div>
                <section
                  :class="[
                    'section section-teachers mx-auto text-center',
                    locale,
                  ]"
                >
                  <h2
                    class="headline d-inline-block"
                    v-html="$t('about_us_page.section_title_1')"
                  ></h2>
                  <p class="text font-weight-bold">
                    {{ $t('about_us_page.teachers_intro') }}
                  </p>
                  <p class="text">
                    {{ $t('about_us_page.teachers_text') }}
                  </p>
                </section>
                <div class="boxes text-center">
                  <div v-for="i in 3" :key="i" class="box">
                    <div class="icon d-flex justify-center">
                      <v-img
                        :src="
                          require(`~/assets/images/about-us-page/box-icon-${i}.svg`)
                        "
                        max-width="132"
                        height="100%"
                        contain
                        center
                        :alt="$t(`about_us_page.box_title_${i}`)"
                      ></v-img>
                    </div>

                    <h3 class="headline d-block font-weight-bold">
                      {{ $t(`about_us_page.box_title_${i}`) }}
                    </h3>
                    <p class="ma-0">
                      {{ $t(`about_us_page.box_text_${i}`) }}
                    </p>
                  </div>
                </div>
                <div class="button-container text-center">
                  <v-btn large color="primary" to="/teachers">
                    {{ $t('about_us_page.meet_our_teachers') }}
                  </v-btn>
                </div>
                <section
                  :class="['section section-team mx-auto text-center', locale]"
                >
                  <h2
                    class="headline d-inline-block"
                    v-html="$t('about_us_page.section_title_2')"
                  ></h2>
                  <p class="text">
                    {{ $t('about_us_page.about_team') }}
                  </p>
                </section>
                <section class="members mx-auto">
                  <div
                    v-for="(member, i) in members"
                    :key="i"
                    :class="['d-sm-flex member', { 'mt-2': i !== 0 }]"
                  >
                    <div class="d-flex d-sm-block">
                      <v-img
                        class="image mr-1 mr-sm-2 elevation-3 rounded-lg"
                        :src="member.photo"
                        :alt="member.name"
                      >
                        <template #placeholder>
                          <v-skeleton-loader
                            type="image"
                            height="100%"
                            width="100%"
                          ></v-skeleton-loader>
                        </template>
                      </v-img>
                      <h3 class="top font-weight-regular d-sm-none">
                        <span class="d-block name orange--text">
                          {{ member.name }}
                        </span>
                        <span class="d-block occupation">
                          {{ member.title }}
                        </span>
                      </h3>
                    </div>
                    <div class="member-content">
                      <h3 class="top font-weight-regular d-none d-sm-block">
                        <span class="d-block name orange--text">
                          {{ member.name }}
                        </span>
                        <span class="d-block occupation">
                          {{ member.title }}
                        </span>
                      </h3>
                      <div class="bio">
                        <p class="bio-inner">
                          {{ member.content }}
                        </p>
                      </div>
                    </div>
                  </div>
                </section>
                <section
                  :class="[
                    'section section-methodology text-center mx-auto',
                    locale,
                  ]"
                >
                  <h2
                    class="headline d-inline-block"
                    v-html="$t('about_us_page.section_title_3')"
                  ></h2>
                  <p class="text">
                    {{ $t('about_us_page.methodology') }}
                  </p>
                  <!-- <div class="mt-1 text-center">
                    <v-btn
                      text
                      large
                      color="orange"
                      to="/how-it-works"
                      class="subtitle-1"
                      >{{ $t('learn_more') }} ></v-btn
                    >
                  </div> -->
                </section>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </div>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: 'AboutUsPage',
  data() {
    return {
      members: [
        {
          name: this.$t('about_us_page.member_travis.name'),
          photo: require('~/assets/images/about-us-page/travis.jpg'),
          title: this.$t('about_us_page.member_travis.title'),
          content: this.$t('about_us_page.member_travis.content'),
        },
        {
          name: this.$t('about_us_page.member_aga.name'),
          photo: require('~/assets/images/about-us-page/aga-nowicka.jpg'),
          title: this.$t('about_us_page.member_aga.title'),
          content: this.$t('about_us_page.member_aga.content'),
        },
        {
          name: this.$t('about_us_page.member_marta.name'),
          photo: require('~/assets/images/about-us-page/marta.jpg'),
          title: this.$t('about_us_page.member_marta.title'),
          content: this.$t('about_us_page.member_marta.content'),
        },
        {
          name: this.$t('about_us_page.member_adrianna.name'),
          photo: require('~/assets/images/about-us-page/adrianna-lembicz.jpg'),
          title: this.$t('about_us_page.member_adrianna.title'),
          content: this.$t('about_us_page.member_adrianna.content'),
        },
        {
          name: this.$t('about_us_page.member_julia.name'),
          photo: require('~/assets/images/about-us-page/julia.jpg'),
          title: this.$t('about_us_page.member_julia.title'),
          content: this.$t('about_us_page.member_julia.content'),
        },
      ],
    }
  },
  head() {
    return {
      title: this.$t('about_us_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('about_us_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('about_us_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('about_us_page.seo_description'),
        },
        { hid: 'og:image', property: 'og:image', content: this.previewImage },
        { hid: 'og:image:width', property: 'og:image:width', content: 800 },
        { hid: 'og:image:height', property: 'og:image:height', content: 396 },
        {
          hid: 'og:image:type',
          property: 'og:image:type',
          content: 'image/jpeg',
        },
      ],
      bodyAttrs: {
        class: 'about-us-page',
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    previewImage() {
      return (
        process.env.NUXT_ENV_URL +
        require(`~/assets/images/about-us-page/aga-and-travis-cambridge-offsite.jpg`)
      )
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/about-us-page.scss';
</style>
