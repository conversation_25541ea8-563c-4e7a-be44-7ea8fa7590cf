// plugins/router.js
export default ({ app, store }) => {
  if (process.client) {
    const allowedUtmTags = [
      'utm_source',
      'utm_campaign',
      'utm_ad',
      'utm_medium',
      'utm_term',
      'utm_content',
      'latest_utm_source',
    ]
    app.router.beforeEach((to, from, next) => {
      // PRIORITY 1: Handle language prefix routing based on login status
      // For logged-in users: ALWAYS remove language prefix
      const isUserLogged = store.getters['user/isUserLogged']
      const toPath = to.path
      const hasLanguagePrefix = /^\/(pl|es)\//.test(toPath)
      const pathWithoutPrefix = toPath.replace(/^\/(pl|es)/, '') || '/'

      if (isUserLogged && hasLanguagePrefix) {
        // Redirect logged-in users to path without language prefix
        console.log(`[Router] Logged-in user detected. Redirecting from ${toPath} to ${pathWithoutPrefix}`)
        return next(pathWithoutPrefix)
      }

      // PRIORITY 2: Continue with UTM tag handling
      if (typeof localStorage !== 'undefined') {
        if (to?.query && Object.keys(to?.query).length) {
          let areUtmTagsInQuery = false
          const utmTagsFromQuery = {}
          Object.keys(to?.query).forEach((key) => {
            if (allowedUtmTags.includes(key)) {
              areUtmTagsInQuery = true
              utmTagsFromQuery[key] = to?.query[key]
            }
          })
          if (areUtmTagsInQuery) {
            localStorage.setItem('utm', JSON.stringify(utmTagsFromQuery))
          }
        } else {
          const storedUTM = JSON.parse(localStorage.getItem('utm') || '{}')
          if (Object.keys(storedUTM).length > 0) {
            const updatedQuery = { ...to.query, ...storedUTM }
            const queryString = new URLSearchParams(updatedQuery).toString()

            if (queryString !== new URLSearchParams(to.query).toString()) {
              return next({ ...to, query: updatedQuery })
            }
          }
        }
      }
      next()
    })
  }
}
