<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
    ></teacher-listing>

    <check-email-dialog
      :show-check-email-dialog="showCheckEmailDialog"
      @close="showCheckEmailDialog = false"
    ></check-email-dialog>

    <set-password-dialog
      :show-set-password-dialog="showSetPasswordDialog"
    ></set-password-dialog>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'
import CheckEmailDialog from '~/components/CheckEmailDialog'
import SetPasswordDialog from '~/components/SetPasswordDialog'
import { getSpecialityId, getLanguageId } from '~/helpers/urlParams'
import { processUrlParameters } from '~/helpers/processUrlParameters'

export default {
  name: 'TeacherListingPagePL',
  components: { TeacherListing, CheckEmailDialog, SetPasswordDialog },
  async asyncData({ store, query, req, redirect }) {
    let filters

    // Set flag to prevent navigation during initial load
    store.commit('teacher_filter/SET_LOADING_FROM_URL', true)

    // Only fetch filters if not already loaded
    if (!store.state.teacher_filter.filters) {
      await store
        .dispatch('teacher_filter/getFilters', { skipAutoSelect: true })
        .then((data) => (filters = data))
    } else {
      filters = store.state.teacher_filter.filters
    }

    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const showCheckEmailDialog = Boolean(+query.checkEmail)
    const searchQuery = query?.search

    const urlParams = store.state.urlParams
    const specialityId = getSpecialityId(filters, urlParams.speciality)
    const languageId = getLanguageId(filters, urlParams.languageToLearn)

    const teachers = await store.dispatch('teacher/getTeachers', {
      specialityId,
      languageId,
      currency: currentCurrency?.code,
      sorting: selectedSorting,
      searchQuery,
    })

    const faqItems = await store.dispatch('faq/getTeacherListingFaqs')

    return {
      teachers,
      faqItems,
      showCheckEmailDialog,
    }
  },
  data() {
    return {
      showSetPasswordDialog: false,
    }
  },
  computed: {
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
  },
  watch: {
    isUserLogged(newVal) {
      if (newVal) {
        this.showSetPasswordDialog = this.$store.state.user.item?.needsPasswordSet
      }
    },
  },
  mounted() {
    this.showSetPasswordDialog = this.$store.state.user.item?.needsPasswordSet
  },
}
</script>

