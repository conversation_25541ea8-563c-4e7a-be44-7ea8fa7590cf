<!-- eslint-disable nuxt/no-env-in-hooks -->
<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="teacher-filter-new">
    <div class="desktop-only">
      <div class="display-flex">
        <v-select
          v-model="selectedLanguage"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          class="l-select teacher-filter-selector mr-3"
          :items="languages"
          :placeholder="
            selectedLanguage &&
            languages.filter((lang) => lang.id === selectedLanguage.id).length
              ? languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                  .name
              : $t('all_languages')
          "
        >
          <template #selection>
            <div
              v-if="
                selectedLanguage &&
                languages.filter((lang) => lang.id === selectedLanguage.id)
                  .length
              "
              class="display-flex"
            >
              <div
                v-if="
                  languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                    .isoCode
                "
                class="icon icon-flag"
              >
                <v-img
                  :src="
                    require(`~/assets/images/flags/${
                      languages.filter(
                        (lang) => lang.id === selectedLanguage.id
                      )[0].isoCode
                    }.svg`)
                  "
                  width="18"
                  height="18"
                ></v-img>
              </div>
              {{
                languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                  .name
              }}
            </div>
            <div v-else>{{ $t('all_languages') }}</div>
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <template v-if="!hideItemIcon">
              <div v-if="item.icon" class="icon">
                <v-img
                  :src="require(`~/assets/images/${item.icon}.svg`)"
                  width="16"
                  height="16"
                ></v-img>
              </div>
              <div v-if="item.isoCode" class="icon icon-flag">
                <v-img
                  :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
                  width="18"
                  height="18"
                ></v-img>
              </div>
            </template>
            <div
              :class="[
                (selectedLanguage && item.id === selectedLanguage.id) ||
                (!selectedLanguage && item.id === null)
                  ? 'selected-text-filter'
                  : 'unselected-text-filter',
              ]"
            >
              {{ ' ' + translation ? $t(item.name) : item.name }}
            </div>
          </template>
        </v-select>
        <v-select
          v-model="selectedMotivation"
          :menu-props="{
            offsetY: true,
            contentClass: 'motivation-menu-content',
            nudgeBottom: 30,
          }"
          class="l-select teacher-filter-selector mr-3 teacher-filter-motivations"
          :items="motivations"
          :placeholder="displayedMotivationText"
        >
          <template v-if="$slots['prepend-inner']" #prepend-inner>
            <slot name="prepend-inner">{{ $t('my_motivation') }}</slot>
          </template>
          <template #selection>
            {{ displayedMotivationText }}
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <div
              class="motivation-item-wrapper"
              @click="handleMotivationClick(item)"
            >
              <div v-if="item.icon" class="icon">
                <svg width="16" height="16" viewBox="0 0 16 16">
                  <use
                    :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                      item.icon
                    }`"
                  ></use>
                </svg>
              </div>
              <div
                :class="[
                  'motivation-item-text',
                  selectedMotivation && item.id === selectedMotivation.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
              >
                {{
                  ' ' + translation
                    ? $t(item.motivationName)
                    : item.motivationName
                }}
              </div>
              <!-- Show arrow if motivation has specialities -->
              <div
                v-if="item.specialities && item.specialities.length"
                class="motivation-arrow"
              >
                <v-icon color="greyDark" size="16">{{
                  mdiChevronRight
                }}</v-icon>
              </div>

              <!-- CSS-only right-side submenu for specialities -->
              <div
                v-if="item.specialities && item.specialities.length"
                class="specialities-css-submenu"
              >
                <div class="specialities-submenu-title">
                  {{ $t(item.motivationName) }}
                </div>
                <div class="specialities-submenu-content">
                  <!-- All option -->
                  <div
                    class="speciality-option"
                    :class="{ selected: !selectedSpecialities.length }"
                    @click.stop="selectSpeciality(item, null)"
                  >
                    <v-icon size="16" class="speciality-radio-icon">
                      {{
                        !selectedSpecialities.length
                          ? 'mdi-radiobox-marked'
                          : 'mdi-radiobox-blank'
                      }}
                    </v-icon>
                    {{ $t('all') }}
                  </div>
                  <!-- Individual specialities -->
                  <div
                    v-for="speciality in item.specialities.filter(
                      (s) => s.isPublish
                    )"
                    :key="speciality.id"
                    class="speciality-option"
                    :class="{ selected: isSpecialitySelected(speciality) }"
                    @click.stop="selectSpeciality(item, speciality)"
                  >
                    <v-icon size="16" class="speciality-radio-icon">
                      {{
                        isSpecialitySelected(speciality)
                          ? 'mdi-radiobox-marked'
                          : 'mdi-radiobox-blank'
                      }}
                    </v-icon>
                    {{ getTranslatedSpecialityName(speciality) }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </v-select>

        <v-select
          class="l-select teacher-filter-selector mr-3"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :placeholder="
            selectedProficiencyLevel &&
            proficiencyLevels.filter(
              (lang) => lang.id === selectedProficiencyLevel.id
            ).length
              ? proficiencyLevels.filter(
                  (lang) => lang.id === selectedProficiencyLevel.id
                )[0].name
              : $t('my_level')
          "
          :items="proficiencyLevels"
        >
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #selection>
            {{
              selectedProficiencyLevel &&
              proficiencyLevels.filter(
                (lang) => lang.id === selectedProficiencyLevel.id
              ).length
                ? proficiencyLevels.filter(
                    (lang) => lang.id === selectedProficiencyLevel.id
                  )[0].name
                : $t('my_level')
            }}
          </template>
          <template #item="{ item }">
            <v-radio-group v-model="selectedProficiencyLevel" hide-details>
              <v-radio
                :key="item.id"
                :label="item.name"
                :class="[
                  'l-radio-button',
                  selectedProficiencyLevel &&
                  item.id === selectedProficiencyLevel.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
                dark
                :ripple="false"
                :value="item"
              >
              </v-radio>
            </v-radio-group>
          </template>
        </v-select>

        <v-select
          class="l-select teacher-filter-selector"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :placeholder="
            (
              selectedDays &&
              days.filter((lang) =>
                selectedDays.map((day) => day.id).includes(lang.id)
              )
            ).length
              ? days
                  .filter((lang) =>
                    selectedDays.map((day) => day.id).includes(lang.id)
                  )
                  .map((day) => capitalizeFirstLetter($t(day.name)))
                  .join(', ')
              : $t('days_per_week')
          "
          :items="days"
        >
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #prepend-item>
            <v-checkbox
              v-model="isSelectedAllDays"
              :label="$t('all')"
              class="l-checkbox custom-all-filters-checkbox"
              hide-details
              :ripple="false"
              @change="allDaysChangeHandler"
            ></v-checkbox>
          </template>
          <template #item="{ item }">
            <v-checkbox
              v-model="selectedDays"
              :value="item"
              :class="[
                'l-checkbox',
                selectedDays && item.id === selectedDays.id
                  ? 'selected-text-filter'
                  : 'unselected-text-filter',
              ]"
              :label="$t(item.name)"
              hide-details
              :ripple="false"
            ></v-checkbox>
          </template>
        </v-select>
      </div>
      <div class="display-flex mt-2">
        <v-select
          class="l-select teacher-filter-selector mr-3"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :placeholder="
            (
              selectedTimes &&
              times.filter((lang) =>
                selectedTimes.map((day) => day.id).includes(lang.id)
              )
            ).length
              ? times
                  .filter((lang) =>
                    selectedTimes.map((day) => day.id).includes(lang.id)
                  )
                  .map((day) => capitalizeFirstLetter($t(day.name)))
                  .join(', ')
              : $t('time_of_day')
          "
          :items="times"
        >
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #prepend-item>
            <v-checkbox
              v-model="isSelectedAllTimes"
              :label="$t('all')"
              class="l-checkbox custom-all-filters-checkbox custom-time-select-box"
              dark
              hide-details
              :ripple="false"
              @change="allTimesChangeHandler"
            ></v-checkbox>
          </template>
          <template #item="{ item }">
            <v-checkbox
              v-model="selectedTimes"
              :value="item"
              :class="[
                'l-checkbox',
                selectedTimes && item.id === selectedTimes.id
                  ? 'selected-text-filter'
                  : 'unselected-text-filter',
              ]"
              hide-details
              :ripple="false"
            >
              <template #label>
                <div class="custom-time-select-box">
                  <div v-if="item.image" class="label-icon label-icon--time">
                    <svg width="16" height="16" viewBox="0 0 16 16">
                      <use
                        :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                          item.image
                        }`"
                      ></use>
                    </svg>
                  </div>
                  {{ $t(item.name) }}&#160;
                  <span
                    :class="[
                      'checkbox-period',
                      selectedTimes && item.id === selectedTimes.id
                        ? 'selected-text-filter'
                        : 'unselected-text-filter',
                    ]"
                  >
                    {{ item.period }}
                  </span>
                </div>
              </template>
            </v-checkbox>
          </template>
          <template #append-item>
            <v-list-item disabled>
              <v-list-item-content style="padding: 0">
                <v-list-item-title class="info-text">
                  <p
                    class="times-filter-info"
                    v-html="
                      $t(
                        'lesson_times_displayed_based_on_your_current_local_time',
                        { time: formatDateTime() }
                      )
                    "
                  ></p>
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </template>
        </v-select>

        <!-- Currency filter - only show for non-logged-in users -->
        <v-select
          v-if="!isUserLogged"
          class="l-select teacher-filter-selector mr-3"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          autowidth
          :placeholder="displayedCurrencyText"
          :items="currencies"
        >
          <template #selection>
            {{ displayedCurrencyText }}
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <v-radio-group v-model="selectedCurrency" hide-details>
              <v-radio
                :key="item.id"
                :label="item.isoCode"
                :class="[
                  'l-radio-button',
                  selectedCurrency && item.id === selectedCurrency.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
                :value="item"
                :ripple="false"
              >
              </v-radio>
            </v-radio-group>
          </template>
        </v-select>

        <v-select
          class="l-select teacher-filter-selector mr-3 teacher-language-preference-filter"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :placeholder="$t('i_prefer_teacher_who')"
          :items="teacherPreferences"
        >
          <template #selection>
            {{ $t('i_prefer_teacher_who') }}
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <v-radio-group v-model="selectedTeacherPreference" hide-details>
              <v-radio
                :key="item.id"
                :label="item.name"
                :class="[
                  'l-radio-button',
                  selectedCurrency && item.id === selectedTeacherPreference.id
                    ? 'v-item--active selected-text-filter'
                    : 'unselected-text-filter',
                  item.id === 2 ? 'teacher-language-preference-filter' : '',
                ]"
                :value="item"
                :ripple="false"
                @click.stop="
                  item.id === 2 && !selectedTeacherPreferenceLanguage
                    ? null
                    : (selectedTeacherPreference = item)
                "
              >
                <template #label>
                  {{ item.name }}
                </template>
              </v-radio>
            </v-radio-group>
          </template>
          <template #append-item>
            <v-list-item class="teacher-filter-flag-subfilter-wrapper">
              <v-list-item-content>
                <v-select
                  ref="preferenceLanguageAutocomplete"
                  v-model="selectedTeacherPreferenceLanguage"
                  class="l-select teacher-filter-selector teacher-filter-flag-subfilter"
                  :items="languages"
                >
                  <template #label style="top: 3px">
                    <span class="custom-label">{{
                      $t('select_language')
                    }}</span>
                  </template>
                  <template #selection>
                    <div
                      v-if="selectedTeacherPreferenceLanguage.isoCode"
                      class="display-flex"
                    >
                      <div class="icon icon-flag">
                        <v-img
                          :src="
                            require(`~/assets/images/flags/${selectedTeacherPreferenceLanguage.isoCode}.svg`)
                          "
                          width="18"
                          height="18"
                        ></v-img>
                      </div>
                      {{ selectedTeacherPreferenceLanguage.name }}
                    </div>
                  </template>
                  <template #append>
                    <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
                  </template>
                  <template #item="{ item }">
                    <template v-if="!hideItemIcon">
                      <div v-if="item.icon" class="icon">
                        <v-img
                          :src="require(`~/assets/images/${item.icon}.svg`)"
                          width="16"
                          height="16"
                        ></v-img>
                      </div>
                      <div v-if="item.isoCode" class="icon icon-flag">
                        <v-img
                          :src="
                            require(`~/assets/images/flags/${item.isoCode}.svg`)
                          "
                          width="18"
                          height="18"
                        ></v-img>
                      </div>
                    </template>
                    {{ ' ' + translation ? $t(item.name) : item.name }}
                  </template>
                </v-select>
              </v-list-item-content>
            </v-list-item>
          </template>
        </v-select>

        <v-select
          class="l-select teacher-filter-selector"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :placeholder="
            selectedFeedbackTag &&
            feedbackTags.filter((lang) => lang.id === selectedFeedbackTag.id)
              .length
              ? feedbackTags.filter(
                  (lang) => lang.id === selectedFeedbackTag.id
                )[0].name
              : $t('unique_qualities')
          "
          :items="feedbackTags"
        >
          <template #selection>
            {{
              selectedFeedbackTag &&
              feedbackTags.filter((lang) => lang.id === selectedFeedbackTag.id)
                .length
                ? feedbackTags.filter(
                    (lang) => lang.id === selectedFeedbackTag.id
                  )[0].name
                : $t('unique_qualities')
            }}
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <v-radio-group v-model="selectedFeedbackTag" hide-details>
              <v-radio
                :key="item.id"
                :label="item.name"
                :class="[
                  'l-radio-button',
                  selectedFeedbackTag && item.id === selectedFeedbackTag.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
                :value="item"
                :ripple="false"
              >
              </v-radio>
            </v-radio-group>
          </template>
        </v-select>

        <!-- Spacer to maintain consistent width when currency filter is hidden -->
        <div
          v-if="isUserLogged"
          class="l-select teacher-filter-selector filter-spacer"
        ></div>
      </div>
    </div>

    <div class="mobile-only">
      <div class="search-wrap">
        <search-input
          v-model.trim="searchQuery_"
          placeholder="search_for_names_or_keywords"
          class="search-input--inner-border"
          @submit="submitSearchForm"
        ></search-input>
      </div>
      <div class="filters-head-title">
        <div class="d-md-inline-block">{{ $t('find_your_teacher') }}</div>
      </div>
      <div class="display-flex mt-2">
        <v-select
          v-model="selectedLanguage"
          class="l-select teacher-filter-selector mr-3"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :items="languages"
          :placeholder="
            selectedLanguage &&
            languages.filter((lang) => lang.id === selectedLanguage.id).length
              ? languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                  .name
              : $t('all_languages')
          "
        >
          <template v-if="$slots['prepend-inner']" #prepend-inner>
            <slot name="prepend-inner">{{ $t('language') }}</slot>
          </template>
          <template #selection>
            <div
              v-if="
                selectedLanguage &&
                languages.filter((lang) => lang.id === selectedLanguage.id)
                  .length
              "
              class="display-flex"
            >
              <div
                v-if="
                  languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                    .isoCode
                "
                class="icon icon-flag"
              >
                <v-img
                  :src="
                    require(`~/assets/images/flags/${
                      languages.filter(
                        (lang) => lang.id === selectedLanguage.id
                      )[0].isoCode
                    }.svg`)
                  "
                  width="18"
                  height="18"
                ></v-img>
              </div>
              {{
                languages.filter((lang) => lang.id === selectedLanguage.id)[0]
                  .name
              }}
            </div>
            <div v-else>{{ $t('all_languages') }}</div>
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <template v-if="!hideItemIcon">
              <div v-if="item.icon" class="icon">
                <v-img
                  :src="require(`~/assets/images/${item.icon}.svg`)"
                  width="16"
                  height="16"
                ></v-img>
              </div>
              <div v-if="item.isoCode" class="icon icon-flag">
                <v-img
                  :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
                  width="18"
                  height="18"
                ></v-img>
              </div>
            </template>
            <div
              :class="[
                (selectedLanguage && item.id === selectedLanguage.id) ||
                (!selectedLanguage && item.id === null)
                  ? 'selected-text-filter'
                  : 'unselected-text-filter',
              ]"
            >
              {{ ' ' + translation ? $t(item.name) : item.name }}
            </div>
          </template>
        </v-select>
        <v-select
          v-model="selectedMotivation"
          class="l-select teacher-filter-selector teacher-filter-motivations"
          :menu-props="{ offsetY: true, nudgeBottom: 30 }"
          :items="motivations"
          :placeholder="
            selectedMotivation &&
            motivations.filter((lang) => lang.id === selectedMotivation.id)
              .length
              ? motivations.filter(
                  (lang) => lang.id === selectedMotivation.id
                )[0].motivationName
              : $t('my_motivation')
          "
        >
          <template #selection>
            {{
              selectedMotivation &&
              motivations.filter((lang) => lang.id === selectedMotivation.id)
                .length
                ? motivations.filter(
                    (lang) => lang.id === selectedMotivation.id
                  )[0].motivationName
                : $t('my_motivation')
            }}
          </template>
          <template #append>
            <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
          </template>
          <template #item="{ item }">
            <div v-if="item.icon" class="icon">
              <svg width="16" height="16" viewBox="0 0 16 16">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                    item.icon
                  }`"
                ></use>
              </svg>
            </div>
            <div
              :class="[
                selectedMotivation && item.id === selectedMotivation.id
                  ? 'selected-text-filter'
                  : 'unselected-text-filter',
              ]"
            >
              {{
                ' ' + translation
                  ? $t(item.motivationName)
                  : item.motivationName
              }}
            </div>
          </template>
        </v-select>
      </div>
      <div v-if="showAllFilters">
        <div class="display-flex mt-2">
          <v-select
            class="l-select teacher-filter-selector mr-3"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="
              selectedProficiencyLevel &&
              proficiencyLevels.filter(
                (lang) => lang.id === selectedProficiencyLevel.id
              ).length
                ? proficiencyLevels.filter(
                    (lang) => lang.id === selectedProficiencyLevel.id
                  )[0].name
                : $t('my_level')
            "
            :items="proficiencyLevels"
          >
            <template #selection>
              {{
                selectedProficiencyLevel &&
                proficiencyLevels.filter(
                  (lang) => lang.id === selectedProficiencyLevel.id
                ).length
                  ? proficiencyLevels.filter(
                      (lang) => lang.id === selectedProficiencyLevel.id
                    )[0].name
                  : $t('my_level')
              }}
            </template>
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>
            <template #item="{ item }">
              <v-radio-group v-model="selectedProficiencyLevel" hide-details>
                <v-radio
                  :key="item.id"
                  :label="item.name"
                  :class="[
                    'l-radio-button',
                    selectedProficiencyLevel &&
                    item.id === selectedProficiencyLevel.id
                      ? 'selected-text-filter'
                      : 'unselected-text-filter',
                  ]"
                  dark
                  :ripple="false"
                  :value="item"
                >
                </v-radio>
              </v-radio-group>
            </template>
          </v-select>
          <v-select
            class="l-select teacher-filter-selector"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="
              (
                selectedDays &&
                days.filter((lang) =>
                  selectedDays.map((day) => day.id).includes(lang.id)
                )
              ).length
                ? days
                    .filter((lang) =>
                      selectedDays.map((day) => day.id).includes(lang.id)
                    )
                    .map((day) => capitalizeFirstLetter($t(day.name)))
                    .join(', ')
                : $t('days_per_week')
            "
            :items="days"
          >
            <template #selection>
              {{
                (
                  selectedDays &&
                  days.filter((lang) =>
                    selectedDays.map((day) => day.id).includes(lang.id)
                  )
                ).length
                  ? days
                      .filter((lang) =>
                        selectedDays.map((day) => day.id).includes(lang.id)
                      )
                      .map((day) => capitalizeFirstLetter($t(day.name)))
                      .join(', ')
                  : $t('days_per_week')
              }}
            </template>
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>
            <template #prepend-item>
              <v-checkbox
                v-model="isSelectedAllDays"
                :label="$t('all')"
                class="l-checkbox custom-all-filters-checkbox"
                hide-details
                :ripple="false"
                @change="allDaysChangeHandler"
              ></v-checkbox>
            </template>
            <template #item="{ item }">
              <v-checkbox
                v-model="selectedDays"
                :value="item"
                :class="[
                  'l-checkbox',
                  selectedDays && item.id === selectedDays.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
                :label="$t(item.name)"
                hide-details
                :ripple="false"
              ></v-checkbox>
            </template>
          </v-select>
        </div>
        <div class="display-flex mt-2">
          <v-select
            :class="[
              'l-select teacher-filter-selector',
              !isUserLogged ? 'mr-3' : '',
            ]"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="
              (
                selectedTimes &&
                times.filter((lang) =>
                  selectedTimes.map((day) => day.id).includes(lang.id)
                )
              ).length
                ? times
                    .filter((lang) =>
                      selectedTimes.map((day) => day.id).includes(lang.id)
                    )
                    .map((day) => capitalizeFirstLetter($t(day.name)))
                    .join(', ')
                : $t('time_of_day')
            "
            :items="times"
          >
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>

            <template #prepend-item>
              <v-checkbox
                v-model="isSelectedAllTimes"
                :label="$t('all')"
                class="l-checkbox custom-all-filters-checkbox custom-time-select-box"
                dark
                hide-details
                :ripple="false"
                @change="allTimesChangeHandler"
              ></v-checkbox>
            </template>
            <template #item="{ item }">
              <v-checkbox
                v-model="selectedTimes"
                :value="item"
                :class="[
                  'l-checkbox',
                  selectedTimes && item.id === selectedTimes.id
                    ? 'selected-text-filter'
                    : 'unselected-text-filter',
                ]"
                hide-details
                :ripple="false"
              >
                <template #label>
                  <div class="custom-time-select-box">
                    <div v-if="item.image" class="label-icon label-icon--time">
                      <svg width="16" height="16" viewBox="0 0 16 16">
                        <use
                          :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                            item.image
                          }`"
                        ></use>
                      </svg>
                    </div>
                    {{ $t(item.name) }}&#160;
                    <span
                      :class="[
                        'checkbox-period',
                        selectedTimes && item.id === selectedTimes.id
                          ? 'selected-text-filter'
                          : 'unselected-text-filter',
                      ]"
                    >
                      {{ item.period }}
                    </span>
                  </div>
                </template>
              </v-checkbox>
            </template>
            <template #append-item>
              <v-list-item disabled>
                <v-list-item-content>
                  <v-list-item-title class="info-text">
                    <p class="times-filter-info">
                      Lesson times are displayed based on your <br />
                      current local time: {{ formatDateTime() }}. <br />
                      Log in to change your time zone.
                    </p>
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-select>
          <!-- Currency filter - only show for non-logged-in users -->
          <v-select
            v-if="!isUserLogged"
            class="l-select teacher-filter-selector teacher-curreny-filter-mobile"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="displayedCurrencyText"
            :items="currencies"
          >
            <template #selection>
              {{ displayedCurrencyText }}
            </template>
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>
            <template #item="{ item }">
              <v-radio-group v-model="selectedCurrency" hide-details>
                <v-radio
                  :key="item.id"
                  :label="item.isoCode"
                  :class="[
                    'l-radio-button',
                    selectedCurrency && item.id === selectedCurrency.id
                      ? 'selected-text-filter'
                      : 'unselected-text-filter',
                  ]"
                  :value="item"
                  :ripple="false"
                >
                </v-radio>
              </v-radio-group>
            </template>
          </v-select>
        </div>
        <div class="display-flex mt-2">
          <v-select
            class="l-select teacher-filter-selector teacher-language-preference-filter mr-3"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="$t('i_prefer_teacher_who')"
            :items="teacherPreferences"
          >
            <template #selection>
              {{ $t('i_prefer_teacher_who') }}
            </template>
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>
            <template #item="{ item }">
              <v-radio-group v-model="selectedTeacherPreference" hide-details>
                <v-radio
                  :key="item.id"
                  :label="item.name"
                  :class="[
                    'l-radio-button',
                    selectedCurrency && item.id === selectedTeacherPreference.id
                      ? 'v-item--active selected-text-filter'
                      : 'unselected-text-filter',
                    item.id === 2 ? 'teacher-language-preference-filter' : '',
                  ]"
                  :value="item"
                  :ripple="false"
                >
                  <template #label>
                    {{ item.name }}
                  </template>
                </v-radio>
              </v-radio-group>
            </template>

            <template #append-item>
              <v-list-item class="teacher-filter-flag-subfilter-wrapper">
                <v-list-item-content>
                  <v-select
                    ref="preferenceLanguageAutocomplete"
                    v-model="selectedTeacherPreferenceLanguage"
                    :menu-props="{
                      offsetY: true,
                      nudgeBottom: 30,
                    }"
                    class="l-select teacher-filter-selector teacher-filter-flag-subfilter"
                    :items="languages"
                    @change="() => (selectedTeacherPreference = { id: 2 })"
                  >
                    <template #label>
                      <span class="custom-label">{{
                        $t('select_language')
                      }}</span>
                    </template>
                    <template #selection>
                      <div
                        v-if="selectedTeacherPreferenceLanguage.isoCode"
                        class="display-flex"
                      >
                        <div class="icon icon-flag">
                          <v-img
                            :src="
                              require(`~/assets/images/flags/${selectedTeacherPreferenceLanguage.isoCode}.svg`)
                            "
                            width="18"
                            height="18"
                          ></v-img>
                        </div>
                        {{ selectedTeacherPreferenceLanguage.name }}
                      </div>
                    </template>

                    <template #append>
                      <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
                    </template>
                    <template #item="{ item }">
                      <template v-if="!hideItemIcon">
                        <div v-if="item.icon" class="icon">
                          <v-img
                            :src="require(`~/assets/images/${item.icon}.svg`)"
                            width="16"
                            height="16"
                          ></v-img>
                        </div>
                        <div v-if="item.isoCode" class="icon icon-flag">
                          <v-img
                            :src="
                              require(`~/assets/images/flags/${item.isoCode}.svg`)
                            "
                            width="18"
                            height="18"
                          ></v-img>
                        </div>
                      </template>
                      {{ ' ' + translation ? $t(item.name) : item.name }}
                    </template>
                  </v-select>
                </v-list-item-content>
              </v-list-item>
            </template>
          </v-select>
          <v-select
            class="l-select teacher-filter-selector"
            :menu-props="{ offsetY: true, nudgeBottom: 30 }"
            :placeholder="
              selectedFeedbackTag &&
              feedbackTags.filter((lang) => lang.id === selectedFeedbackTag.id)
                .length
                ? feedbackTags.filter(
                    (lang) => lang.id === selectedFeedbackTag.id
                  )[0].name
                : $t('unique_qualities')
            "
            :items="feedbackTags"
          >
            <template #selection>
              {{
                selectedFeedbackTag &&
                feedbackTags.filter(
                  (lang) => lang.id === selectedFeedbackTag.id
                ).length
                  ? feedbackTags.filter(
                      (lang) => lang.id === selectedFeedbackTag.id
                    )[0].name
                  : $t('unique_qualities')
              }}
            </template>
            <template #append>
              <v-icon color="greyDark">{{ mdiChevronDown }}</v-icon>
            </template>
            <template #item="{ item }">
              <v-radio-group v-model="selectedFeedbackTag" hide-details>
                <v-radio
                  :key="item.id"
                  :label="item.name"
                  :class="[
                    'l-radio-button',
                    selectedFeedbackTag && item.id === selectedFeedbackTag.id
                      ? 'selected-text-filter'
                      : 'unselected-text-filter',
                  ]"
                  :value="item"
                  :ripple="false"
                >
                </v-radio>
              </v-radio-group>
            </template>
          </v-select>
        </div>
      </div>
    </div>
    <div class="show-all-filters-button" @click="onShowAllFilters()">
      {{ $t(showAllFilters ? 'hide_all_filters' : 'show_all_filters') }}
      <v-icon color="greyDark">{{
        showAllFilters ? mdiChevronUp : mdiChevronDown
      }}</v-icon>
    </div>
  </div>
</template>

<script>
// import LChip from '~/components/LChip'
// import LessonTimeNotice from '~/components/LessonTimeNotice'
import { mdiChevronDown, mdiChevronUp, mdiChevronRight } from '@mdi/js'
import SearchInput from '~/components/form/SearchInput'
import { formatProficiencyLevel } from '~/helpers/proficiency-levels'

export default {
  name: 'TeacherFilter',
  // components: { LChip, LessonTimeNotice },
  components: { SearchInput },
  data() {
    return {
      panel: 0,
      isSelectedAllTimesProxy: false,
      isSelectedAllDaysProxy: false,
      mdiChevronDown,
      mdiChevronUp,
      mdiChevronRight,
      searchQuery_: null,
      showAllFilters: false,
      showSpecialitiesForMotivation: null, // Track which motivation should show specialities
      hideItemIcon: false, // Control whether to show icons in dropdown items
    }
  },
  computed: {
    getCurrencySetByUser() {
      return this.$store.getters['teacher_filter/getCurrencySetByUser']
    },
    feedbackTags() {
      return this.$store.getters['teacher_filter/feedbackTags']
    },
    languageChip() {
      return this.$store.getters['teacher_filter/languageChip']
    },
    motivationChip() {
      return this.$store.getters['teacher_filter/motivationChip']
    },
    specialityChips() {
      return this.$store.getters['teacher_filter/specialityChips']
    },
    proficiencyLevelChip() {
      return this.$store.getters['teacher_filter/proficiencyLevelChip']
    },
    teacherPreferenceChip() {
      return this.$store.getters['teacher_filter/teacherPreferenceChip']
    },
    teacherMatchLanguageChip() {
      return this.$store.getters['teacher_filter/teacherMatchLanguageChip']
    },
    dateChips() {
      return this.$store.getters['teacher_filter/dateChips']
    },
    timeChips() {
      return this.$store.getters['teacher_filter/timeChips']
    },
    currencyChip() {
      return this.$store.getters['teacher_filter/currencyChip']
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    filters() {
      return this.$store.state.teacher_filter.filters
    },
    languages() {
      const filteredLanguages =
        this.filters?.languages
          ?.filter((item) => item.uiAvailable)
          ?.sort((a, b) => a.name.localeCompare(b.name, this.$i18n.locale)) ||
        []

      // Add "All Languages" option at the beginning
      return [
        {
          id: null,
          name: this.$t('all_languages'),
          isoCode: null,
          uiAvailable: true,
        },
        ...filteredLanguages,
      ]
    },
    motivations() {
      return this.filters?.motivations || []
    },
    specialities() {
      return this.$store.getters['teacher_filter/publishSpecialities']
    },
    proficiencyLevels() {
      // Backend already returns translated names, no need to translate again
      return this.filters?.proficiencyLevels || []
    },
    teacherPreferences() {
      return [
        {
          id: 0,
          name: this.$t('prefer_title1'),
        },
        {
          id: 1,
          name: this.$t('prefer_title2'),
        },
        {
          id: 2,
          name: this.$t('prefer_title3'),
        },
      ]
    },
    days() {
      return this.$store.getters['teacher_filter/days']
    },
    times() {
      return this.$store.getters['teacher_filter/times']
    },
    currencies() {
      return this.filters?.currencies || []
    },
    selectedLanguage: {
      get() {
        return this.$store.getters['teacher_filter/selectedLanguage']
      },
      set(item) {
        // Handle "All Languages" selection (item is null or has id === null)
        if (!item || item.id === null) {
          // User selected "All Languages" - reset language and remember this choice
          this.$store.commit('teacher_filter/RESET_SELECTED_LANGUAGE')
          if (process.client && typeof window !== 'undefined') {
            window.sessionStorage.setItem('isLanguageFilterRemoved', 'true')
          }
        } else {
          // User selected a specific language
          this.$store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language: item,
          })
          if (process.client && typeof window !== 'undefined') {
            // Clear the "removed" flag so English can auto-select on future visits
            window.sessionStorage.removeItem('isLanguageFilterRemoved')
          }
        }

        this.submitFormHandler()
      },
    },

    selectedSpecialities: {
      get() {
        return this.$store.getters['teacher_filter/selectedSpecialities']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
          specialities: items,
        })
        this.submitFormHandler()
      },
    },
    selectedMotivation: {
      get() {
        return this.$store.getters['teacher_filter/selectedMotivation']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_MOTIVATION', {
          motivation: item,
        })
        this.submitFormHandler()
      },
    },
    selectedDays: {
      get() {
        return this.$store.getters['teacher_filter/selectedDays']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_DAYS', { dates: items })
        this.submitFormHandler()
      },
    },
    selectedTimes: {
      get() {
        return this.$store.getters['teacher_filter/selectedTimes']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times: items,
        })
        this.submitFormHandler()
      },
    },
    selectedProficiencyLevel: {
      get() {
        return this.$store.getters['teacher_filter/selectedProficiencyLevel']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
          proficiencyLevel: item,
        })
        this.submitFormHandler()
      },
    },
    selectedTeacherPreference: {
      get() {
        return this.$store.getters['teacher_filter/selectedTeacherPreference']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
          teacherPreference: item,
        })
        this.$store.commit(
          'teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE_LANGUAGE'
        )
        if (item.id !== 2) this.submitFormHandler()
      },
    },
    selectedTeacherPreferenceLanguage: {
      get() {
        return this.$store.getters[
          'teacher_filter/selectedTeacherPreferenceLanguage'
        ]
      },
      set(item) {
        this.$store.commit(
          'teacher_filter/SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE',
          { teacherPreferenceLanguage: item }
        )
        if (
          this.$store.getters['teacher_filter/selectedTeacherPreference'].id ===
          0
        ) {
          this.$store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
            teacherPreference: { id: 2, name: this.$t('prefer_title3') },
          })
        }
        this.submitFormHandler()
      },
    },
    selectedCurrency: {
      get() {
        const { id } = this.$store.state.currency.item
        return this.filters?.currencies?.find((item) => item.id === id)
      },
      set(item) {
        this.$store.dispatch('currency/setItem', { item })
        // Don't set currency as set by user to prevent filter pills from appearing
        // this.$store.dispatch('teacher_filter/setCurrencyByUser', {
        //   setByUser: true,
        // })
        this.submitFormHandler()
      },
    },
    selectedFeedbackTag: {
      get() {
        return this.$store.getters['teacher_filter/selectedFeedbackTag']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', item)
        this.submitFormHandler()
      },
    },
    hasSelectedFeedbackTag() {
      return this.$store.getters['teacher_filter/hasSelectedFeedbackTag']
    },
    searchQuery() {
      return this.$store.getters['teacher_filter/searchQuery']
    },
    selectedSorting() {
      return this.$store.getters['teacher_filter/selectedSorting']
    },
    needUpdateTeachers() {
      return this.$store.state.teacher_filter.needUpdateTeachers
    },
    isSelectedAllDays: {
      get() {
        return this.isSelectedAllDaysProxy
      },
      set(value) {
        this.isSelectedAllDaysProxy = value
      },
    },
    isSelectedAllTimes: {
      get() {
        return this.isSelectedAllTimesProxy
      },
      set(value) {
        this.isSelectedAllTimesProxy = value
      },
    },
    isShownTeacherFilter() {
      return this.$store.state.isShownTeacherFilter
    },
    displayedMotivationText() {
      // If a specialty is selected, show the specialty name
      if (this.selectedSpecialities && this.selectedSpecialities.length > 0) {
        return this.selectedSpecialities[0].name
      }
      // If a motivation is selected but no specialty, show the motivation name
      if (
        this.selectedMotivation &&
        this.motivations.filter(
          (lang) => lang.id === this.selectedMotivation.id
        ).length
      ) {
        return this.motivations.filter(
          (lang) => lang.id === this.selectedMotivation.id
        )[0].motivationName
      }
      // Default placeholder
      return this.$t('my_motivation')
    },
    displayedCurrencyText() {
      // For non-logged-in users, show the selected currency or GBP as default
      if (
        this.selectedCurrency &&
        this.currencies.filter((curr) => curr.id === this.selectedCurrency.id)
          .length
      ) {
        return this.currencies.filter(
          (curr) => curr.id === this.selectedCurrency.id
        )[0].isoCode
      }
      // Default to GBP for non-logged-in users
      return 'GBP'
    },
  },
  watch: {
    needUpdateTeachers(newValue) {
      if (newValue) {
        this.submitFormHandler()
      }
    },
    isShownTeacherFilter(newValue) {
      if (newValue) {
        this.openLanguageMenu()
      }
    },
    selectedMotivation(newValue) {
      if (newValue && newValue.specialities) {
        this.$store.commit(
          'teacher_filter/SET_SPECIALITIES',
          newValue.specialities
        )
      } else {
        this.$store.commit('teacher_filter/SET_SPECIALITIES', [])
      }
    },
  },
  beforeMount() {
    // eslint-disable-next-line nuxt/no-env-in-hooks
    if (process.client && typeof window !== 'undefined') {
      const activeFilterPanel = window.sessionStorage.getItem(
        'active-filter-panel'
      )

      if (activeFilterPanel) {
        this.panel = +activeFilterPanel
      } else {
        window.sessionStorage.setItem('active-filter-panel', '0')
      }

      if (
        !this.selectedLanguage?.id &&
        window.sessionStorage.getItem('isLanguageFilterRemoved') !== 'true'
      ) {
        const userLanguageId = this.$store?.state?.settings?.languagesItem
          ?.languagesTaught?.[0]?.id
        this.selectedLanguage = { id: userLanguageId ?? 12 }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.isSelectedAllDays = this.selectedDays.length === this.days.length
      this.isSelectedAllTimes = this.selectedTimes.length === this.times.length

      if (this.$vuetify.breakpoint.mdAndUp) {
        this.openLanguageMenu()
      }

      this.$emit('filters-loaded')
    })
  },
  methods: {
    translateProficiencyLevel(levelName) {
      if (!levelName || !this.$t || typeof this.$t !== 'function')
        return levelName
      return formatProficiencyLevel(levelName, this.$t)
    },
    getTranslatedSpecialityName(speciality) {
      if (
        !speciality ||
        !speciality.translations ||
        !this.$i18n ||
        !this.$i18n.locale
      ) {
        return speciality?.name || ''
      }
      const currentLocale = this.$i18n.locale
      const translation = speciality.translations.find(
        (t) => t.locale === currentLocale && t.field === 'name'
      )
      return translation ? translation.content : speciality.name
    },
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1)
    },
    handleMotivationClick(motivation) {
      // If motivation has no specialities, select it directly
      if (!motivation.specialities || !motivation.specialities.length) {
        this.selectedMotivation = motivation
      }
      // If it has specialities, the submenu will handle the selection
    },
    selectSpeciality(motivation, speciality) {
      // Set the motivation
      this.selectedMotivation = motivation

      // Set the speciality selection
      if (speciality === null) {
        // "All" selected - clear speciality selection
        this.selectedSpecialities = []
      } else {
        // Specific speciality selected
        this.selectedSpecialities = [speciality]
      }
    },
    isSpecialitySelected(speciality) {
      return this.selectedSpecialities.some((s) => s.id === speciality.id)
    },
    toggleSpecialitySelection(speciality) {
      const currentSpecialities = [...this.selectedSpecialities]
      const index = currentSpecialities.findIndex((s) => s.id === speciality.id)

      if (index > -1) {
        // Remove if already selected
        currentSpecialities.splice(index, 1)
      } else {
        // Add if not selected
        currentSpecialities.push(speciality)
      }

      this.selectedSpecialities = currentSpecialities
    },
    toggleSpecialitiesDisplay(motivation) {
      // Toggle the display of specialities for the clicked motivation
      if (
        this.showSpecialitiesForMotivation &&
        this.showSpecialitiesForMotivation.id === motivation.id
      ) {
        // If already showing specialities for this motivation, hide them
        this.showSpecialitiesForMotivation = null
      } else {
        // Show specialities for this motivation
        this.showSpecialitiesForMotivation = motivation
        // Also select the motivation
        this.selectedMotivation = motivation
      }
    },
    onShowAllFilters() {
      this.showAllFilters = !this.showAllFilters
    },
    fetchData() {
      this.$store.commit('teacher_filter/SET_NEED_UPDATE_TEACHERS', true)
    },
    submitSearchForm() {
      this.searchQuery = this.searchQuery_

      this.fetchData()
    },
    feedbackTagClickHandler(tag) {
      this.selectedFeedbackTag = tag

      this.fetchData()
    },
    formatDateTime() {
      const date = new Date()
      let hours = date.getHours()
      const minutes = date.getMinutes()
      const isAM = hours < 12
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes
      const amPm = isAM ? 'AM' : 'PM'
      hours = hours % 12 || 12
      const timezoneOffsetMinutes = date.getTimezoneOffset()
      const timezoneSign = timezoneOffsetMinutes <= 0 ? '+' : '-'
      const absOffsetMinutes = Math.abs(timezoneOffsetMinutes)
      const offsetHours = Math.floor(absOffsetMinutes / 60)
      const offsetMinutes = absOffsetMinutes % 60
      const formattedOffset = `GMT ${timezoneSign}${offsetHours}:${
        offsetMinutes < 10 ? '0' : ''
      }${offsetMinutes}`
      return `${hours}:${formattedMinutes} ${amPm} (${formattedOffset})`
    },
    openLanguageMenu() {
      window.setTimeout(() => {
        if (this.panel === 0 && !this.selectedLanguage) {
          this.$refs.languageAutocomplete?.focus()
          this.$refs.languageAutocomplete?.activateMenu()
        }

        if (
          this.panel === 3 &&
          this.selectedTeacherPreference.id === 2 &&
          !this.selectedTeacherPreferenceLanguage
        ) {
          this.$refs.preferenceLanguageAutocomplete?.focus()
          this.$refs.preferenceLanguageAutocomplete?.activateMenu()
        }
      }, 100)
    },
    setActivePanel(id) {
      this.panel = id

      if (id !== undefined) {
        this.openLanguageMenu()
        window.sessionStorage.setItem('active-filter-panel', id)
      } else {
        window.sessionStorage.removeItem('active-filter-panel')
      }
    },
    isOpenedPanel(id) {
      return +this.panel === id
    },
    allDaysChangeHandler(e) {
      if (e) {
        this.selectedDays = this.days
      } else {
        this.resetDays()
      }
    },
    allTimesChangeHandler(e) {
      if (e) {
        this.selectedTimes = this.times
      } else {
        this.resetTimes()
      }
    },
    resetLanguage() {
      this.$store.commit('teacher_filter/RESET_SELECTED_LANGUAGE')
      if (process.client && typeof window !== 'undefined') {
        window.sessionStorage.setItem('isLanguageFilterRemoved', 'true')
      }
      this.submitFormHandler()
    },
    resetDays() {
      this.$store.commit('teacher_filter/RESET_SELECTED_DAYS')
      this.submitFormHandler()
    },
    resetTimes() {
      this.$store.commit('teacher_filter/RESET_SELECTED_TIMES')
      this.submitFormHandler()
    },
    resetSpeciality(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_SPECIALITIES', item)
      this.submitFormHandler()
    },
    resetMotivation() {
      this.$store.commit('teacher_filter/RESET_SELECTED_MOTIVATION')
      this.submitFormHandler()
    },
    resetTeacherPreference() {
      this.$store.commit('teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE')
      this.submitFormHandler()
    },
    resetDay(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_DAYS', item)
      this.submitFormHandler()
    },
    resetTime(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_TIMES', item)
      this.submitFormHandler()
    },
    resetLevel() {
      this.$store.commit('teacher_filter/RESET_SELECTED_PROFICIENCY_LEVEL')
      this.submitFormHandler()
    },
    async resetCurrency() {
      await this.$store.dispatch('teacher_filter/resetCurrency')
      this.submitFormHandler()
    },
    resetAllClickHandler() {
      this.setActivePanel(0)
      this.$router.push({
        path: '/teacher-listing',
        params: {},
        query: {},
      })
    },
    closeTeacherFilterClickHandler() {
      this.$store.commit('SET_IS_TEACHER_FILTER', false)
    },
    submitFormHandler() {
      let params = ''

      if (this.selectedLanguage) {
        params += `language,${this.selectedLanguage.id};`
      }

      // Only send motivation parameter if no specialities are selected
      // When specialities are selected, we want to filter only by speciality, not by motivation
      if (this.selectedMotivation && !this.selectedSpecialities?.length) {
        params += `motivation,${this.selectedMotivation.id};`
      }

      if (this.selectedSpecialities?.length) {
        params += `speciality,${this.selectedSpecialities
          .map((item) => item.id)
          .join(',')};`
      }

      if (this.selectedDays.length) {
        params += `dates,${this.selectedDays.map((item) => item.id).join(',')};`
      }

      if (this.selectedTimes.length) {
        params += `time,${this.selectedTimes.map((item) => item.id).join(',')};`
      }

      if (this.selectedProficiencyLevel) {
        params += `proficiencyLevels,${this.selectedProficiencyLevel.id};`
      }

      if (
        this.selectedTeacherPreference &&
        this.selectedTeacherPreference.id !== 0
      ) {
        params += `teacherPreference,${
          this.selectedTeacherPreferenceLanguage
            ? 2
            : this.selectedTeacherPreference.id
        };`

        if (this.selectedTeacherPreferenceLanguage) {
          params += `matchLanguages,${this.selectedTeacherPreferenceLanguage.id};`
        }
      }

      if (this.selectedFeedbackTag) {
        params += `tag,${this.selectedFeedbackTag.id};`
      }

      params += `sortOption,${
        this.selectedFeedbackTag && this.selectedSorting.isFeedbackTag
          ? 8
          : this.selectedSorting.id
      };`
      params += `currency,${this.selectedCurrency?.id || 1}`

      if (
        this.$store.getters['auth/getPasswordTokenItem'] === null ||
        this.$store.getters['auth/getPasswordTokenItem']?.isExpired
      ) {
        if (!this.$router?.currentRoute?.query?.checkEmail) {
          this.$router.push({
            path: `/teacher-listing/1/${params}`,
            query: this.searchQuery ? { search: this.searchQuery } : {},
          })
        }
      }
    },
  },
}
</script>

<style>
.show-all-filters-button {
  display: none;
}

.teacher-filter-selector {
  box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  -webkit-box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  -moz-box-shadow: 0 4px 14px rgba(217, 225, 236, 0.47);
  border-radius: 50px;
  width: 25%;
  background-color: white;
  border: 1px solid #d1d1d1;
}

.filter-spacer {
  /* Invisible spacer to maintain consistent width when currency filter is hidden */
  visibility: hidden;
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  background-color: transparent;
  border: none;
  margin-right: 0;
  margin-right: 20px;
}

.teacher-filter-selector .v-select__slot {
  margin-top: 5px;
  margin-bottom: -15px;
  padding-right: 20px;
  padding-left: 20px;
}

.teacher-filter-selector .v-input__append-inner {
  margin-right: 10px;
  margin-top: 1px;
}

.icon,
.icon-flag {
  margin-right: 10px;
}

.teacher-filter-selector .v-select__selections input::placeholder {
  font-weight: bold;
  color: black;
}

.display-flex {
  display: flex;
}

.v-menu__content,
.menuable__content__active {
  border-radius: 25px;
}

.v-menu__content,
.menuable__content__active .v-label {
  font-size: 16px;
  font-weight: 600;
}

.level-selection,
.v-label {
  font-weight: 600 !important;
}

.l-select .v-select__selections {
  font-weight: bold !important;
}

.l-radio-button .v-input--selection-controls__input::before,
.l-radio-button .v-input--selection-controls__input::after {
  margin-right: 10px;
}

.v-list .v-list-item--active {
  color: white !important;
}

.v-input--selection-controls .v-input__slot > .v-label,
.v-input--selection-controls .v-radio > .v-label {
  margin-left: 10px;
}

.custom-time-select-box,
.custom-time-select-box .v-label {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  margin-top: -5px;
}

.v-list,
.v-select-list {
  padding-left: 10px;
}

.teacher-filter-motivations,
.v-list .v-list-item--active {
  color: inherit !important;
}

.v-input--selection-controls {
  margin-top: 0 !important;
}

.sub-field-selector {
  width: 100px;
  margin-left: 10px;
  box-shadow: none;
  border-radius: 10px;
  height: 30px;
}

.teacher-filter-flag-subfilter {
  box-shadow: none;
  color: inherit;
  width: 50px;
  padding: 0px;
  height: 30px;
  margin: 0px !important;
  margin-left: 10px !important;
  border-radius: 10px;
}

.teacher-filter-flag-subfilter .v-select__slot {
  margin: 0px;
  padding-left: 10px;
}

.teacher-filter-flag-subfilter .v-select__selections {
  padding: 0px;
}

.teacher-filter-flag-subfilter .v-icon {
  display: block !important;
  margin-top: -8px !important;
}

.teacher-filter-flag-subfilter .v-select__slot {
  padding: 0px;
  padding-left: 5px;
}
#list-item-170-2
  > div
  > div
  > div
  > div
  > div
  > div
  .listbox
  > div
  > div
  > div
  > div
  > div
  > div {
  margin-top: 8px;
}

.v-list-item__title {
  display: flex;
}

.v-list-item--active::before {
  opacity: 0 !important;
}

.v-menu__content {
  -ms-overflow-style: none;
  scrollbar-width: 5px;
}

.v-menu__content::-webkit-scrollbar {
  display: none;
  width: 5px;
  height: 10px;
}

.selected-text-filter {
  color: var(--v-success-base);
}

#selected-text-filter {
  color: var(--v-success-base);
}

.selected-text-filter > label {
  color: var(--v-success-base) !important;
}

#selected-text-filter > label {
  color: var(--v-success-base) !important;
}

.unselected-text-filter > label {
  color: black !important;
}

.times-filter-info {
  font-size: 12px;
  margin-bottom: 0px !important;
}

.l-radio-button.v-item--active .v-input--selection-controls__input::after {
  background-color: transparent;
}

.teacher-filter-new .l-radio-button .v-input--selection-controls__input::before,
.teacher-filter-new .l-radio-button .v-input--selection-controls__input::after {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 18px;
  width: 18px;
}
.teacher-filter-new .v-text-field .v-label {
  top: 0;
}
.l-radio-button.v-item--active .v-input--selection-controls__input::after {
  background-image: url('~assets/images/radio-button-selected.svg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.l-radio-button.theme--dark .v-input--selection-controls__input::before {
  border: 1px solid var(--v-greyDark-base);
}

.custom-all-filters-checkbox {
  height: 30px;
  margin-left: 16px;
  margin-top: 15px !important;
  margin-bottom: 4px;
}

.custom-all-filters-checkbox .v-input--selection-controls__input::after {
  border: 1px solid var(--v-greyDark-base);
}

.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media screen and (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }

  .teacher-filter-selector {
    width: 50%;
  }

  .show-all-filters-button {
    display: flex;
  }
}
</style>
<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.search-wrap {
  padding-right: 24px;

  @media only screen and (min-width: $xsm-and-up) {
    flex: 1 0 auto;
    display: flex;
    align-items: center;
  }

  @media only screen and (max-width: $mac-13-and-down) {
    padding-right: 18px;
  }

  @media #{map-get($display-breakpoints, 'xs-only')} {
    padding-right: 0;
    margin-top: 20px;
  }

  @media only screen and (max-width: $xsm-and-down) {
    width: 100%;
  }

  .v-form {
    width: 100%;
    max-width: 580px;
    min-width: 310px;

    @media only screen and (max-width: $xsm-and-down) {
      max-width: 100%;
      min-width: auto;
    }
  }
}

.filters-head-title {
  font-size: 18px !important;
  font-weight: bold;
  margin-top: 30px;
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  border-bottom: 2px solid #ecf3ff;
  padding-bottom: 20px;
}

.filters-head-title .v-select__slot {
  padding-left: 120px;
  margin-top: -2px;
  background: #f8faff !important;
}

.filters-head-title .v-select__slot {
  padding-left: 120px !important;
}

.filters-head-title .v-input__append-inner {
  margin-right: -3px;
}

.filters-head-title .l-select.v-select--is-menu-active .v-input__append-inner {
  margin-right: -28px !important;
  margin-top: -4px;
}

.show-all-filters-button {
  justify-content: flex-start;
  place-items: center;
  font-size: 16px;
  font-weight: bold;
  margin-top: 20px;
}

.chip {
  position: relative;
  display: inline-flex;
  align-items: center;
  min-height: 32px;
  color: #fff;
  font-size: 14px;
  line-height: 1;
  letter-spacing: 0.3px;
  border-radius: 16px;
  border: 1px solid #314869;
  transition: border-color 0.3s;

  &::before {
    border-radius: inherit;
  }

  & > div {
    position: relative;
    padding: 4px 12px;
  }

  .teacher-listing-wrap &:not(.chip--transparent) {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        126.15deg,
        var(--v-success-base) 0%,
        var(--v-primary-base) 102.93%
      ) !important;
      opacity: 1;
      box-shadow: 0px 3px 16px -5px rgba(0, 0, 0, 0.73);
      border: none !important;
    }
  }

  .teacher-curreny-filter-mobile .v-input__append-inner {
    margin: 0 !important;
  }
}

.teacher-listing-wrap .text--gradient {
  color: white !important;
  -webkit-text-fill-color: white !important;
}

.teacher-listing-wrap .unselected-text-filter {
  color: black !important;
}

.teacher-filter-flag-subfilter-wrapper {
  margin-left: 20px;
  margin-top: -10px;
}

.teacher-filter-flag-subfilter-wrapper .v-select__selections {
  background: transparent;
  padding-left: 10px;
  border-radius: 20px;
}
.teacher-filter-flag-subfilter-wrapper .custom-label {
  padding-left: 16px;
  margin-bottom: 10px;
}

.motivation-specialities-subfilter-wrapper {
  margin-left: 20px;
  margin-top: -10px;
}

.motivation-specialities-subfilter-wrapper .specialities-submenu-title {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: var(--v-greyDark-base) !important;
  margin-bottom: 12px !important;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.motivation-specialities-subfilter-wrapper .speciality-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 13px;
  color: var(--v-greyDark-base);
}

.motivation-specialities-subfilter-wrapper .speciality-option:hover {
  background-color: #f5f5f5;
}

.motivation-specialities-subfilter-wrapper .speciality-option.selected {
  background-color: #e3f2fd;
  color: var(--v-primary-base);
}

.motivation-speciality-subfilter {
  margin-top: 8px;
}

.motivation-speciality-subfilter .custom-label {
  font-size: 14px;
  color: var(--v-greyDark-base);
  font-weight: 500;
}

.motivation-specialities-subfilter-wrapper {
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  padding: 12px 16px;
}

.teacher-filter-new .v-text-field__details {
  min-height: 0;
}

.teacher-filter-new .v-text-field__details .v-messages {
  min-height: 0;
}

.teacher-filter-new .v-input__slot {
  margin-bottom: 23px;
}

@media (max-width: 767px) {
  .show-all-filters-button {
    justify-content: center;
  }

  .mobile-only .teacher-listing-header {
    max-width: 478px;
    margin: 0 auto;
    margin-top: 20px;
  }
}

.teacher-filter-new .l-select .v-select__selections {
  color: black !important;
}

/* Motivation item wrapper styles */
.motivation-item-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 16px;
  cursor: pointer;
  position: relative;
  margin-right: 10px;
}

.motivation-item-wrapper:hover {
  background-color: #f5f5f5;
}

.motivation-item-text {
  flex: 1;
  margin-left: 8px;
}

.motivation-arrow {
  margin-left: auto;
  display: flex;
  align-items: center;
}

/* CSS-only right-side specialities submenu */
.specialities-css-submenu {
  position: absolute;
  left: 100%; /* Position right next to the menu item */
  top: 0;
  min-width: 220px; /* Increased minimum width */
  width: max-content; /* Allow content to determine width */
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  padding: 16px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  max-height: 350px;
  overflow-y: auto;
  margin-left: 30px;
}
.motivation-menu-content {
  overflow: visible !important;
  contain: none !important;
}

/* Target ONLY the motivation dropdown's list */
.motivation-menu-content .v-list {
  overflow: visible !important;
  background: white !important;
  border-radius: 25px !important;
}

/* Scrollbar styling */
.specialities-css-submenu::-webkit-scrollbar {
  width: 6px;
}

.specialities-css-submenu::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.specialities-css-submenu::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.specialities-css-submenu::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
/* Show submenu on hover */
.motivation-item-wrapper:hover .specialities-css-submenu {
  opacity: 1;
  visibility: visible;
}

.specialities-submenu-title {
  font-weight: 600 !important;
  font-size: 14px !important;
  color: var(--v-greyDark-base) !important;
  margin-bottom: 12px !important;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
  margin-left: 16px;
}

.specialities-submenu-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.speciality-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 13px;
}

.speciality-option:hover {
  background-color: #f5f5f5;
}

.speciality-option.selected {
  background: linear-gradient(
    126.15deg,
    rgba(128, 182, 34, 0.1) 0%,
    rgba(60, 135, 248, 0.1) 102.93%
  );
}

.speciality-radio-icon {
  margin-right: 8px !important;
  color: var(--v-primary-base) !important;
}

/* Ensure the submenu appears above other elements */
.v-list-item {
  position: relative;
}
</style>
