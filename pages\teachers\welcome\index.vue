<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :faq-items="faqItems"
    ></teacher-listing>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'

export default {
  name: 'TeacherListingPage',
  components: { TeacherListing },
  async middleware({ store, redirect }) {
    if (
      store.getters['user/isStudent'] &&
      !store.getters['user/registrationConfirmed']
    ) {
      await store
        .dispatch('auth/confirmRegistration')
        .catch(() => redirect('/teachers'))
    } else {
      return redirect('/teachers')
    }
  },
  async asyncData({ store }) {
    let filters

    await store
      .dispatch('teacher_filter/getFilters')
      .then((data) => (filters = data))
    await store.dispatch('teacher_filter/resetSorting')
    await store.dispatch('teacher_filter/resetFilters')

    const currentCurrency = store.state.currency.item
    const selectedSorting = store.state.teacher_filter.selectedSorting
    const params = `currency,${currentCurrency.id};sortOption,${selectedSorting.id}`

    await store.dispatch('teacher/getTeachers', {
      page: 1,
      perPage: process.env.NUXT_ENV_PER_PAGE,
      params,
    })

    return { filters }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n?.locale || 'en'
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
