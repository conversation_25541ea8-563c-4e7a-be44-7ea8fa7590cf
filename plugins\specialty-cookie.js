// Plugin to handle specialty cookie persistence
export default ({ store, $cookiz }) => {
  console.log('[DEBUG] specialty-cookie plugin - Initializing')
  console.log('[DEBUG] specialty-cookie plugin - $cookiz available:', !!$cookiz)

  // Subscribe to store mutations to save specialty to cookie
  store.subscribe((mutation, state) => {
    // Watch for specialty selection changes
    if (mutation.type === 'teacher_filter/SET_SELECTED_SPECIALITIES') {
      console.log(
        '[DEBUG] specialty-cookie plugin - Mutation detected:',
        mutation.type
      )
      console.log(
        '[DEBUG] specialty-cookie plugin - selectedSpecialities:',
        state.teacher_filter.selectedSpecialities
      )

      const selectedSpecialities = state.teacher_filter.selectedSpecialities
      if (selectedSpecialities && selectedSpecialities.length > 0) {
        const specialtyIds = selectedSpecialities.map((s) => s.id).join(',')
        console.log(
          '[DEBUG] specialty-cookie plugin - Saving specialty to cookie:',
          specialtyIds
        )

        try {
          // Use simple options without domain parameter
          $cookiz.set('selected_specialty', specialtyIds, {
            maxAge: 60 * 60 * 24 * 30, // 30 days
          })
          console.log(
            '[DEBUG] specialty-cookie plugin - Cookie saved successfully'
          )
        } catch (error) {
          console.error(
            '[DEBUG] specialty-cookie plugin - Error saving cookie:',
            error
          )
        }
      } else {
        // Clear cookie if no specialties selected
        console.log(
          '[DEBUG] specialty-cookie plugin - Clearing specialty cookie'
        )
        try {
          $cookiz.remove('selected_specialty')
          console.log(
            '[DEBUG] specialty-cookie plugin - Cookie cleared successfully'
          )
        } catch (error) {
          console.error(
            '[DEBUG] specialty-cookie plugin - Error clearing cookie:',
            error
          )
        }
      }
    }
  })

  console.log('[DEBUG] specialty-cookie plugin - Initialized successfully')
}
