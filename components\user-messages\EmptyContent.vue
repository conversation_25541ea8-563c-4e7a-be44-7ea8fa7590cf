<template>
  <div class="messages-empty-content">
    <div class="messages-empty-content-title font-weight-medium mb-3 mb-sm-4">
      {{
        $t(isTeacher ? 'no_messages_teacher_yet' : 'no_messages_student_yet')
      }}
      💬
    </div>
    <div class="messages-empty-content-text">
      <template v-if="isTeacher">
        <template v-if="locale === 'pl'">
          Kiedy uczeń rezerwuje lekcję próbną, jest poproszony o napisanie
          krótkiej wiadomości, która pojawi się tutaj.
          <br /><br />
          Zanim to się stanie, upewnij się, że Twoja strona profilowa wzbudza
          jak największe zainteresowanie.
          <br /><br />
          <ul class="mb-0">
            <li>Dodaj wysokiej jakości wideo na YouTube</li>
            <li>
              Zwróć uwagę na dodanie przyjaznego zdjęcia profilowego (powinno
              by<PERSON> jasne i przejrzyste)
            </li>
            <li>
              Dodaj Kursy do swojego profilu, aby zaprezentować swoją
              specjalistyczną wiedzę
            </li>
            <li>Spraw, żeby Twój profil się wyróżniał!</li>
          </ul>
          <br />
          Możesz także udostępnić link do swojego profilu nauczyciela. Wyślij go
          do poprzednich uczniów, opublikuj w mediach społecznościowych lub
          reklamuj w kanałach lokalnych.
        </template>
        <template v-else-if="locale === 'es'">
          Cuando un estudiante reserva una lección de prueba, debe escribir un
          mensaje de introducción, que aparecerá aquí.
          <br /><br />
          Hasta entonces, asegúrese de que su perfil docente sea lo más
          atractivo posible.
          <br /><br />
          <ul class="mb-0">
            <li>Agregar un video de YouTube de alta calidad</li>
            <li>
              Agregue una foto de perfil de bienvenida (brillante y clara)
            </li>
            <li>
              Agregue uno o más cursos a su perfil para mostrar su experiencia
            </li>
            <li>¡Haz que tu personalidad destaque!</li>
          </ul>
          <br />
          También puede compartir un enlace a su perfil de maestro. Envíelo a
          los alumnos anteriores, publíquelo en las redes sociales o publíquelo
          en los canales locales.
        </template>
        <template v-else>
          When a student books a trial lesson, they must write an intro message,
          which will appear here. You may also receive questions without a trial
          booking.
          <br /><br />
          Until then, make sure to make your teaching profile as engaging as
          possible.
          <br /><br />
          <ul class="mb-0">
            <li>Add a high-quality YouTube video</li>
            <li>Add a welcoming profile photo (bright and clear)</li>
            <li>
              Add one or more Courses to your profile to showcase your expertise
            </li>
            <li>Make your personality stand out!</li>
          </ul>
          <br />
          You can also share a link to your teacher profile. Send it to previous
          students, post it on social media, or advertise it in local channels.
        </template>

        <template v-if="teacherSlug">
          <br /><br />
          <template v-if="$vuetify.breakpoint.smAndUp">
            <nuxt-link :to="{ path: `/teacher/${teacherSlug}` }">
              {{ profileLink }}
            </nuxt-link>
          </template>
          <template v-else>
            <div class="d-flex align-center">
              <svg class="mr-1" width="20" height="20" viewBox="0 0 20 20">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#user-icon`"
                ></use>
              </svg>
              <div>{{ teacherSlug }}</div>
              <div class="d-flex align-center text--gradient ml-2">
                <v-img
                  class="mr-1"
                  :src="require('~/assets/images/copy-icon-gradient.svg')"
                  width="16"
                  height="16"
                ></v-img>
                <input ref="profileLink" type="text" class="d-none" />
                <div @click="copyLink">
                  {{ $t('copy_link') }}
                </div>
              </div>
            </div>
          </template>
        </template>
      </template>
      <template v-else>
        <template v-if="locale === 'pl'">
          Wejdź na stronę
          <nuxt-link to="/teachers">"Znajdź nauczyciela"</nuxt-link>, aby wybrać
          swojego korepetytora, zarezerwować lekcję próbną lub zapytać
          nauczyciela jak może pomóc Ci z osiągnięciem Twoich celów
          językowych.<br /><br />
          Nie zapomnij odwiedzić naszej strony
          <nuxt-link to="/faq">FAQ</nuxt-link>, jeśli nie masz pewności, jak
          korzystać z Langu!
        </template>
        <template v-else-if="locale === 'es'">
          Consulte la página
          <nuxt-link to="/teachers">Encontrar un Profesor</nuxt-link>
          para elegir un maestro, reservar una lección de prueba o hacer una
          pregunta sobre cómo un maestro puede ayudarlo a alcanzar sus
          objetivos. <br /><br />
          ¡Y asegúrese de visitar nuestra página
          <nuxt-link to="/faq">de preguntas frecuentes</nuxt-link> si no está
          seguro de cómo funciona Langu!
        </template>
        <template v-else>
          Check out the
          <nuxt-link to="/teachers">Find a Teacher</nuxt-link> page to pick a
          teacher, book a trial lesson, or ask a question about how a teacher
          can help you achieve your goals. <br /><br />
          And be sure to visit our <nuxt-link href="/faq">FAQ</nuxt-link> page
          if you’re not sure how Langu works!
        </template>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserMessagesEmptyContent',
  computed: {
    locale() {
      return this.$i18n.locale
    },
    isTeacher() {
      return this.$store.getters['user/isTeacher']
    },
    teacherSlug() {
      return this.$store.getters['user/teacherSlug']
    },
    profileLink() {
      return this.teacherSlug
        ? `${process.env.NUXT_ENV_URL}/teacher/${this.teacherSlug}`
        : null
    },
  },
  methods: {
    copyLink() {
      try {
        const el = this.$refs.profileLink

        el.setAttribute('value', this.profileLink)
        el.select()
        el.setSelectionRange(0, 99999)

        navigator.clipboard.writeText(el.value)

        this.$store.dispatch('snackbar/success', {
          successMessage: 'link_copied',
          timeout: 1500,
        })
      } catch (e) {
        console.log(e)
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.messages-empty-content {
  padding: 30px 44px 138px;
  background-color: #fff;
  box-shadow: 0 8px 17px rgba(17, 46, 90, 0.1);
  border-radius: 20px;
  line-height: 1.4;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    padding: 24px 24px 60px;
  }

  @media #{map-get($display-breakpoints, 'sm-and-up')} {
    min-height: 620px;
  }

  @media only screen and (max-width: $xxs-and-down) {
    padding: 24px 15px 30px;
  }

  &-title {
    font-size: 24px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 20px;
    }
  }

  &-text {
    font-size: 18px;

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
      font-size: 16px;
    }

    a {
      color: var(--v-success-base);
      background: linear-gradient(
        -75deg,
        var(--v-success-base),
        var(--v-primary-base)
      );
      background: -moz-linear-gradient(
        -75deg,
        var(--v-success-base),
        var(--v-primary-base)
      );
      background: -webkit-linear-gradient(
        -75deg,
        var(--v-success-base),
        var(--v-primary-base)
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    ul {
      padding-left: 32px;
    }
  }
}
</style>
