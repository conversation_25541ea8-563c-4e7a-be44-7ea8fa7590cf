<template>
  <l-dialog
    :dialog="showSetPasswordDialog"
    :hide-close-button="isValid"
    max-width="388"
    custom-class="set-password"
    @close-dialog="resetData"
  >
    <div>
      <template v-if="isValid">
        <div class="set-password-title">
          {{ $t('thank_you_for_confirming_your_email_address') }}
        </div>
        <div class="set-password-text mt-3 mt-md-5">
          {{ $t('please_create_your_langu_password') }}
        </div>
        <form class="mt-2" @submit.prevent="setPasswordSubmitHandler">
          <div class="mb-3">
            <text-input
              v-model="password"
              type="password"
              :placeholder="$t('password')"
              name="password"
              hide-details
              autocomplete="new-password"
            >
              <template #append>
                <div style="margin-top: 5px">
                  <v-img
                    :src="require('~/assets/images/lock-icon.svg')"
                    width="14"
                    height="21"
                  ></v-img>
                </div>
              </template>
            </text-input>
          </div>
          <div>
            <text-input
              v-model="passwordRepeat"
              type="password"
              :placeholder="$t('repeat_password')"
              name="confirmPassword"
              hide-details
              autocomplete="new-password"
            >
              <template #append>
                <div style="margin-top: 5px">
                  <v-img
                    :src="require('~/assets/images/lock-icon.svg')"
                    width="14"
                    height="21"
                  ></v-img>
                </div>
              </template>
            </text-input>
          </div>
          <div class="form-message">
            <template v-if="passwordLengthError">
              <div class="form-message-wrap form-message-wrap--error">
                <div class="form-message-icon">
                  <svg width="12" height="12" viewBox="0 0 12 12">
                    <use
                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#attention`"
                    ></use>
                  </svg>
                </div>
                {{ passwordTextError }}
              </div>
            </template>
          </div>
          <div class="set-password-button mt-1">
            <v-btn
              color="orange"
              class="font-weight-medium"
              x-large
              type="submit"
            >
              {{ $t('save') }}
            </v-btn>
          </div>
        </form>
      </template>
      <template v-else>
        <template v-if="token">
          <div class="set-password-title">
            {{ $t('sorry_this_link_has_expired') }}
          </div>
          <div class="set-password-text mt-3 mt-md-5">
            {{
              $t('please_request_new_link_by_clicking_forgot_password_button')
            }}
          </div>
        </template>
        <template v-else>
          <div class="set-password-title">Something went wrong!</div>
        </template>
      </template>
    </div>
  </l-dialog>
</template>

<script>
import { applyNavigationState } from '@/helpers/navigationState'
import LDialog from '~/components/LDialog'
import TextInput from '~/components/form/TextInput'

export default {
  name: 'SetPasswordDialog',
  components: { LDialog, TextInput },
  props: {
    showSetPasswordDialog: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      password: '',
      passwordRepeat: '',
      passwordTextError: '',
    }
  },
  computed: {
    passwordLengthError() {
      return !!this.passwordTextError.length
    },
    token() {
      return this.$store.state.auth.passwordTokenItem?.token
    },
    isValid() {
      return this.token
        ? !this.$store.state.auth.passwordTokenItem?.isExpired ?? false
        : false
    },
  },
  watch: {
    password() {
      this.passwordTextError = ''
    },
    passwordRepeat() {
      this.passwordTextError = ''
    },
  },
  beforeDestroy() {
    this.resetData()
  },
  methods: {
    setPasswordSubmitHandler() {
      this.passwordTextError = ''

      const regex = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,}$/

      if (this.password !== this.passwordRepeat) {
        this.passwordTextError = this.$t('passwords_are_different')

        return
      }

      if (!regex.test(this.password)) {
        this.passwordTextError = this.$t('password_error')

        return
      }

      this.$store
        .dispatch('auth/setPassword', {
          token: this.token,
          password: this.password,
          confirmPassword: this.passwordRepeat,
        })
        .then(() => {
          this.$store.dispatch('user/getUserStatus').then(() => {
            this.resetData()

            // Try to apply the saved navigation state
            const navigationStateApplied = applyNavigationState(
              this.$router,
              true
            )

            // If navigation state was applied, we're done
            if (navigationStateApplied) {
              return
            }

            // Otherwise, use the redirectUrl or default path
            const redirectUrl = this.$store.getters['user/redirectUrl']
            if (redirectUrl) {
              this.$router.push(redirectUrl)
              this.$store.dispatch('user/clearRedirectUrl')
            } else if (
              this.$store.getters['user/isStudent'] &&
              !this.$store.getters['user/registrationConfirmed']
            ) {
              this.$router.push({ path: '/teachers/welcome' })
            }
          })
        })
        .catch((e) => {
          if (e.response) {
            if (e.response.status === 400) {
              this.passwordTextError = this.$t('password_error')
            }

            if (e.response.status === 404) {
              this.$store.commit('SET_IS_PASSWORD_LINK_EXPIRED', true)
              this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null)
              this.$store.commit('SET_IS_LOGIN_SIDEBAR', true)
            }
          }
        })
    },
    resetData() {
      this.password = ''
      this.passwordRepeat = ''

      this.$store.commit('auth/SET_PASSWORD_TOKEN_ITEM', null)
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';

.set-password {
  .set-password {
    &-title {
      color: var(--v-dark-base) !important;
      font-size: 20px;
      font-weight: 600;

      @media only screen and (max-width: $xsm-and-down) {
        text-align: center;
        font-size: 18px;
      }
    }

    &-text {
      color: var(--v-dark-base) !important;
      font-size: 16px;

      @media only screen and (max-width: $xsm-and-down) {
        font-size: 15px;
      }

      @media only screen and (max-width: $xxs-and-down) {
        font-size: 14px;
      }
    }

    &-button {
      text-align: right;

      @media only screen and (max-width: $xsm-and-down) {
        text-align: center;
      }

      .v-btn {
        border-radius: 16px !important;

        .v-btn__content {
          font-weight: 600 !important;
        }
      }
    }
  }
}
</style>
