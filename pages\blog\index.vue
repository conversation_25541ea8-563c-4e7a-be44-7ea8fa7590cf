<template>
  <div id="dib-posts"></div>
</template>

<script>
export default {
  head() {
    return {
      title:
        this.$i18n.locale === 'pl'
          ? 'Langu | Blog: Artykuły i zasoby do efektywnej nauki języków online'
          : 'Langu | Blog: Articles and Resources for Effective Online Language Learning',
    }
  },
  mounted() {
    this.loadDropInBlogScript()
  },
  beforeDestroy() {
    // Clean up any DropInBlog related globals to prevent conflicts
    if (typeof window.dibMenu !== 'undefined') {
      delete window.dibMenu
    }
  },
  methods: {
    loadDropInBlogScript() {
      // Clear any existing DropInBlog globals to prevent conflicts
      if (typeof window.dibMenu !== 'undefined') {
        delete window.dibMenu
      }

      // Check if the script is already loaded and main function exists
      if (typeof window.main === 'function') {
        try {
          window.main()
        } catch (error) {
          // eslint-disable-next-line no-console
          console.warn('Error calling window.main:', error)
          // If there's an error, try to reload the script
          this.forceReloadScript()
        }
        return
      }

      // Check if script is already being loaded or exists
      const existingScript = document.querySelector(
        'script[src*="dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445"]'
      )

      if (existingScript) {
        // Remove existing script to prevent conflicts
        existingScript.remove()
        // Wait a bit then reload
        setTimeout(() => {
          this.forceReloadScript()
        }, 100)
        return
      }

      // Create and load the script
      this.forceReloadScript()
    },
    forceReloadScript() {
      const script = document.createElement('script')
      script.src =
        'https://io.dropinblog.com/embedjs/4cb8aef9-8b09-41c6-83c0-efadfe825445.js'
      script.async = true
      script.defer = true
      script.onload = () => {
        try {
          if (typeof window.main === 'function') {
            window.main()
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('Error initializing DropInBlog:', error)
        }
      }
      script.onerror = () => {
        // eslint-disable-next-line no-console
        console.error('Failed to load DropInBlog script')
      }
      document.head.appendChild(script)
    },
  },
}
</script>

<style lang="scss">
@import '~assets/styles/faq-page.scss';
</style>
