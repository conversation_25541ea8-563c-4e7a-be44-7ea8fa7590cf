import https from 'https'

export default function ({ $axios, store }) {
  $axios.defaults.httpsAgent = new https.Agent({ rejectUnauthorized: false })

  $axios.onRequest((config) => {
    store.dispatch('loadingStart')

    // const authCookie = store.state.authCookie
    const username = process.env.NUXT_ENV_API_USERNAME
    const password = process.env.NUXT_ENV_API_PASSWORD

    if (username && password) {
      config.auth = {
        username,
        password,
      }
    }

    // if (authCookie) {
    //   config.headers.common.Cookie = `L2SESSID=${authCookie};`
    // }

    config.withCredentials = true

    // Ensure headers and headers.common exist before setting locale
    if (!config.headers) {
      config.headers = {}
    }
    if (!config.headers.common) {
      config.headers.common = {}
    }
    config.headers.common.locale = store.state.locale
  })

  $axios.onResponse((data) => {
    store.dispatch('loadingStop')
  })

  $axios.onError((e) => {
    store.dispatch('loadingStop')
  })
}
