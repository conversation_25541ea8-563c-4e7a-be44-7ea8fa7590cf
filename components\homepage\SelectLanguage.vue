<template>
  <div class="select-language">
    <div id="intro-select" class="intro-select mr-4">
      <v-select
        v-model="selectedItemId"
        :items="items"
        item-text="name"
        item-value="id"
        :placeholder="$t('home_page.i_want_to_speak')"
        dense
        outlined
        hide-details
        height="44"
        color="orange"
        hide-selected
        hide-no-data
        :menu-props="{
          bottom: true,
          offsetY: true,
          nudgeBottom: 5,
          contentClass: 'select-language-list',
          maxHeight: maxHeightDropdown,
        }"
      >
        <template #item="{ item }">
          <div v-if="item.id !== selectedItemId" class="v-list-item__content">
            <div class="v-list-item__title">
              {{ $t(item.name) }}
            </div>
            <div class="v-list-item__icon">
              <v-img
                :src="require(`~/assets/images/flags/${item.isoCode}.svg`)"
              ></v-img>
            </div>
          </div>
        </template>
        <template v-if="items.length > 4" #append-item>
          <div class="v-list-item">
            <div class="v-list-item__content v-list-item__content--show-more">
              <div class="v-list-item__title">
                <nuxt-link :to="localePath({ name: 'teachers' })">
                  {{ $t('see_more') }}...
                </nuxt-link>
              </div>
            </div>
          </div>
        </template>
      </v-select>
    </div>
    <v-btn large :to="link" color="primary">{{ $t('lets_go') }}</v-btn>
  </div>
</template>

<script>
export default {
  name: 'SelectLanguage',
  props: {
    items: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      selectedItemId: null,
    }
  },
  computed: {
    link() {
      return this.selectedItemId
        ? `/teachers/1/language,${this.selectedItemId}`
        : '/teachers'
    },
    maxHeightDropdown() {
      return this.items.length > 4 ? 175 : 'auto'
    },
  },
}
</script>

<style lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import '~assets/styles/vars';

.select-language {
  display: flex;

  @media #{map-get($display-breakpoints, 'md-and-down')} {
    justify-content: center;
  }

  @media only screen and (max-width: $xsm-and-down) {
    display: block;

    .v-btn {
      width: 100%;
    }
  }

  .v-select {
    &.v-text-field--outlined {
      border-radius: 24px;

      & > .v-input__control > .v-input__slot,
      .v-text-field__details {
        padding: 0 12px 0 16px !important;
      }

      fieldset,
      &.v-input--is-focused fieldset,
      &.v-input--has-state fieldset {
        border: 1px solid var(--v-orange-base) !important;
      }
    }

    .v-icon {
      color: var(--v-orange-base) !important;
    }

    .v-select__slot {
      display: flex;
      align-items: center;
    }

    .v-select__selections {
      color: #fff !important;
      font-size: 16px !important;
      font-weight: 300 !important;
    }

    input {
      &::placeholder {
        /* Chrome, Firefox, Opera, Safari 10.1+ */
        color: #fff !important;
        font-size: 16px !important;
        font-weight: 300 !important;
        opacity: 1;
      }

      &:-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #fff !important;
        font-size: 16px !important;
        font-weight: 300 !important;
      }

      &::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #fff !important;
        font-size: 16px !important;
        font-weight: 300 !important;
      }
    }
  }
}

.select-language-list {
  border: 1px solid var(--v-orange-base);
  border-radius: 24px !important;
  box-shadow: none !important;

  .v-select-list {
    padding: 5px 0 !important;
    background-color: var(--v-darkLight-base) !important;
  }

  .theme--light.v-list-item:not(.v-list-item--active):not(.v-list-item--disabled) {
    color: #fff !important;
  }

  .v-list-item {
    min-height: 42px !important;
    padding: 0 15px !important;

    a {
      color: #fff !important;
      text-decoration: none;
    }

    &:hover,
    &--active {
      color: #fff !important;

      &::before {
        background-color: var(--v-orangeDark-base);
        border-radius: 2px !important;
        opacity: 1 !important;
      }

      .v-list-item__content::after {
        display: none;
      }
    }

    &:last-child .v-list-item__content {
      border-bottom: none;
    }

    &__content {
      position: relative;
      min-height: 42px;
      padding: 0 !important;
      border-bottom: 1px solid rgba(251, 176, 59, 0.2);
    }

    &__title {
      font-size: 16px !important;
      font-weight: 300 !important;
      color: #fff !important;
    }

    &__icon {
      position: absolute;
      top: 50%;
      right: 0;
      width: 25px;
      height: 25px !important;
      margin-top: -12.5px !important;
      border-radius: 50%;
      overflow: hidden;
    }
  }
}

@media screen and (max-width: 768px) {
  #intro-select {
    margin-right: 0 !important;
    margin-bottom: 20px;
  }
}
</style>
