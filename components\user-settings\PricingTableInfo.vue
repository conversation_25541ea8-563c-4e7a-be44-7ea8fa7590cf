<template>
  <user-setting-template
    v-if="item"
    :title="$t('pricing_table')"
    :submit-func="submitData"
  >
    <div class="mb-3 mb-md-4">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('currency') }}
            </div>
            <div class="input-wrap-label">
              {{
                $t(
                  'once_saved_you_cannot_change_your_currency_if_you_need_to_change_your_payment_currency'
                )
              }}
              <a class="text--gradient" href="mailto:<EMAIL>"
                ><EMAIL></a
              >.
            </div>
            <div class="current-currency d-flex align-center">
              <svg class="mr-1" width="20" height="20" viewBox="0 0 20 20">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#dollar-coin`"
                ></use>
              </svg>
              {{ currentCurrency.isoCode }}
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div class="mb-3 mb-md-4">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('trial_lesson_pricing') }}
            </div>
            <div>
              <v-radio-group
                :value="selectedTrialLessonPricing"
                hide-details
                class="mt-0 pt-0"
                @change="updateTrial"
              >
                <div
                  v-for="(tl, idx) in trialLessonPricingItems"
                  :key="tl.id"
                  :class="{ 'mb-2': idx < trialLessonPricingItems.length - 1 }"
                >
                  <div class="radiobutton d-flex align-center">
                    <v-radio
                      class="d-flex align-center l-radio-button l-radio-button--type-2 mb-0"
                      color="success"
                      :ripple="false"
                      :value="tl.id"
                    >
                      <template #label>
                        {{ $t(tl.name) }}
                      </template>
                    </v-radio>
                    <template v-if="tl.id === 2">
                      <div class="currency-input price-input ml-2">
                        <lesson-price
                          :value="item.priceTrialLesson"
                          :rules="
                            selectedTrialLessonPricing === 2
                              ? trialPriceRules
                              : []
                          "
                          :length="30"
                          :free-trial="true"
                          @input="updateValue($event, 'priceTrialLesson')"
                        ></lesson-price>
                      </div>
                    </template>
                  </div>
                </div>
              </v-radio-group>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div class="mb-3 mb-md-4">
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('new_students') }}
            </div>
            <div>
              <v-radio-group
                :value="item.acceptNewStudents"
                hide-details
                class="mt-0 pt-0"
                @change="updateValue($event, 'acceptNewStudents')"
              >
                <div
                  v-for="(ns, idx) in newStudentsItems"
                  :key="ns.id"
                  :class="{ 'mb-2': idx < newStudentsItems.length - 1 }"
                >
                  <div class="radiobutton">
                    <v-radio
                      class="d-flex align-center l-radio-button l-radio-button--type-2"
                      color="success"
                      :ripple="false"
                      :value="ns.value"
                    >
                      <template #label>
                        {{ $t(ns.name) }}
                      </template>
                    </v-radio>
                  </div>
                </div>
              </v-radio-group>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
    <div>
      <v-row>
        <v-col class="col-12 col-md-10">
          <div class="input-wrap">
            <div class="input-wrap-title body-1 font-weight-medium mb-1">
              {{ $t('lesson_pricing') }}
              <span>({{ $t('enter_prices_per_lesson') }})</span>
            </div>
            <div class="input-wrap-label mb-3">
              {{ $t('all_60_pricing_options_are_required') }}
              <span v-if="item && item.commission">
                ({{ $t('my_commission_rate') }} {{ item.commission }}%)
              </span>
            </div>
            <div class="lesson-pricing">
              <div class="lesson-pricing-row">
                <div class="d-flex align-center justify-center item mr-md-4">
                  <svg width="20" height="20" viewBox="0 0 15 15">
                    <use
                      :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#clock`"
                    ></use>
                  </svg>
                </div>
                <div class="item mr-1 mr-lg-2">
                  {{ $tc('lessons_count', 1) }}
                </div>
                <div class="item mr-1 mr-lg-2">
                  {{ $tc('lessons_count', 5) }}
                </div>
                <div class="item mr-1 mr-lg-2">
                  {{ $tc('lessons_count', 10) }}
                </div>
                <div class="item">{{ $tc('lessons_count', 20) }}</div>
              </div>
              <div class="lesson-pricing-row">
                <div class="item mr-md-4">30’</div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="30"
                    :lessons="1"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="30"
                    :lessons="5"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="30"
                    :lessons="10"
                  ></per-lesson-price>
                </div>
                <div class="item price-input py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="30"
                    :lessons="20"
                  ></per-lesson-price>
                </div>
                <div v-if="getMinimumTextVisibility" class="item_text py-2">
                  <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                  <span class="pl-1">
                    {{ $t('minimum_Text')
                    }}{{
                      `${pricingMap[currentCurrencyIsoCode]['30']} ${currentCurrencyIsoCode}`
                    }}
                  </span>
                </div>
              </div>
              <div v-if="!getMinimumTextVisibility" class="item_text">
                <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                <span class="pl-1">
                  {{ $t('minimum_Text')
                  }}{{
                    `${pricingMap[currentCurrencyIsoCode]['30']} ${currentCurrencyIsoCode}`
                  }}
                </span>
              </div>
              <div class="lesson-pricing-row">
                <div class="item mr-md-4">60’</div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="60"
                    :lessons="1"
                    :rules="priceRules"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="60"
                    :lessons="5"
                    :rules="priceRules"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="60"
                    :lessons="10"
                    :rules="priceRules"
                  ></per-lesson-price>
                </div>
                <div class="item price-input py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="60"
                    :lessons="20"
                    :rules="priceRules"
                  ></per-lesson-price>
                </div>
                <div v-if="getMinimumTextVisibility" class="item_text py-2">
                  <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                  <span class="pl-1">
                    {{ $t('minimum_Text')
                    }}{{
                      `${pricingMap[currentCurrencyIsoCode]['60']} ${currentCurrencyIsoCode}`
                    }}
                  </span>
                </div>
              </div>
              <div v-if="!getMinimumTextVisibility" class="item_text">
                <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                <span class="pl-1">
                  {{ $t('minimum_Text')
                  }}{{
                    `${pricingMap[currentCurrencyIsoCode]['60']} ${currentCurrencyIsoCode}`
                  }}
                </span>
              </div>
              <div class="lesson-pricing-row">
                <div class="item mr-md-4">90’</div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="90"
                    :lessons="1"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="90"
                    :lessons="5"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="90"
                    :lessons="10"
                  ></per-lesson-price>
                </div>
                <div class="item price-input py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="90"
                    :lessons="20"
                  ></per-lesson-price>
                </div>
                <div v-if="getMinimumTextVisibility" class="item_text py-2">
                  <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                  <span class="pl-1">
                    {{ $t('minimum_Text')
                    }}{{
                      `${pricingMap[currentCurrencyIsoCode]['90']} ${currentCurrencyIsoCode}`
                    }}
                  </span>
                </div>
              </div>
              <div v-if="!getMinimumTextVisibility" class="item_text">
                <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                <span class="pl-1">
                  {{ $t('minimum_Text')
                  }}{{
                    `${pricingMap[currentCurrencyIsoCode]['90']} ${currentCurrencyIsoCode}`
                  }}
                </span>
              </div>
              <div class="lesson-pricing-row">
                <div class="item mr-md-4">120’</div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="120"
                    :lessons="1"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="120"
                    :lessons="5"
                  ></per-lesson-price>
                </div>
                <div class="item price-input mr-1 mr-lg-2 py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="120"
                    :lessons="10"
                  ></per-lesson-price>
                </div>
                <div class="item price-input py-2">
                  <per-lesson-price
                    :items="lessonPrices"
                    :length="120"
                    :lessons="20"
                  ></per-lesson-price>
                </div>
                <div v-if="getMinimumTextVisibility" class="item_text py-2">
                  <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                  <span class="pl-1">
                    {{ $t('minimum_Text')
                    }}{{
                      `${pricingMap[currentCurrencyIsoCode]['120']} ${currentCurrencyIsoCode}`
                    }}
                  </span>
                </div>
              </div>
              <div v-if="!getMinimumTextVisibility" class="item_text">
                <v-icon color="greyDark">{{ mdiInformationOutline }}</v-icon>
                <span class="pl-1">
                  {{ $t('minimum_Text')
                  }}{{
                    `${pricingMap[currentCurrencyIsoCode]['120']} ${currentCurrencyIsoCode}`
                  }}
                </span>
              </div>
            </div>
          </div>
        </v-col>
      </v-row>
    </div>
  </user-setting-template>
</template>

<script>
import { mdiInformationOutline } from '@mdi/js'
import UserSettingTemplate from '@/components/user-settings/UserSettingTemplate'
import LessonPrice from '@/components/user-settings/LessonPrice'
import PerLessonPrice from '@/components/user-settings/PerLessonPrice'

export default {
  name: 'PricingTableInfo',
  components: { UserSettingTemplate, LessonPrice, PerLessonPrice },
  data() {
    return {
      mdiInformationOutline,
      trialPriceRules: [(v) => !!v],
      priceRules: [(v) => !!v],
      trialLessonPricingItems: [
        {
          id: 1,
          name: 'i_offer_free_30_minute_trial_lesson_recommended',
          property: 'freeTrialLesson',
        },
        {
          id: 2,
          name: 'i_offer_30_minute_trial_lesson_for',
          property: 'trialLesson',
        },
        // {
        //   id: 3,
        //   name: 'i_do_not_offer_trial_option',
        //   property: 'trialNotProvided',
        // },
      ],
      newStudentsItems: [
        {
          id: 1,
          name: 'i_am_currently_accepting_new_students',
          value: true,
        },
        {
          id: 2,
          name: 'i_am_only_accepting_bookings_from_past_current_students',
          value: false,
        },
      ],
      pricingMap: {
        EUR: { 30: 7, 60: 11, 90: 16, 120: 21 },
        GBP: { 30: 6, 60: 10, 90: 15, 120: 20 },
        PLN: { 30: 30, 60: 50, 90: 70, 120: 85 },
        USD: { 30: 8, 60: 12, 90: 17, 120: 22 },
        AUD: { 30: 12, 60: 20, 90: 28, 120: 36 },
        CAD: { 30: 11, 60: 18, 90: 25, 120: 32 },
      },
    }
  },
  computed: {
    item() {
      return this.$store.state.settings.pricingTableItem
    },
    lessonPrices() {
      return this.$store.state.settings.lessonPrices
    },
    selectedTrialLessonPricing() {
      let value = 3

      if (this.item?.freeTrialLesson) {
        value = 1
      } else if (this.item?.trialLesson) {
        value = 2
      }

      return value
    },
    currentCurrency() {
      return this.$store.state.currency.item
    },
    currentCurrencyIsoCode() {
      return this.$store.state.currency.item?.isoCode
        ? this.$store.state.currency.item?.isoCode.toUpperCase()
        : 'USD'
    },
    getMinimumTextVisibility() {
      return this.$vuetify.breakpoint.mdAndUp
    },
  },
  watch: {
    selectedTrialLessonPricing() {
      this.$emit('validate')
    },
  },
  beforeCreate() {
    this.$store.dispatch('settings/getPricingTable')
  },
  methods: {
    updateTrial(id) {
      const [trialPackage] = this.trialLessonPricingItems.filter(
        (item) => item.id === id
      )

      if (trialPackage.property === 'freeTrialLesson') {
        this.updateValue(true, 'freeTrialLesson')
        this.updateValue(false, 'trialLesson')
      }

      if (trialPackage.property === 'trialLesson') {
        this.updateValue(false, 'freeTrialLesson')
        this.updateValue(true, 'trialLesson')
      }

      // if (trialPackage.property === 'trialNotProvided') {
      //   this.updateValue(false, 'freeTrialLesson')
      //   this.updateValue(false, 'trialLesson')
      // }
    },
    updateValue(value, property) {
      this.$store.commit('settings/UPDATE_PRICING_TABLE_ITEM', {
        [property]: value,
      })
    },
    submitData() {
      this.$store.dispatch('settings/updatePricingTable')
    },
  },
}
</script>

<style scoped lang="scss">
@import '~vuetify/src/styles/settings/_variables';
@import './assets/styles/vars';

.current-currency {
  font-size: 16px;
}

.currency-input {
  width: 84px;
}

.lesson-pricing {
  display: inline-block;

  @media only screen and (max-width: $xxs-and-down) {
    width: calc(100% + 20px);
    margin-left: -10px;
  }

  &-row {
    position: relative;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: calc(100% - 67px);
      height: 1px;
      background-color: rgba(45, 45, 45, 0.06);

      @media #{map-get($display-breakpoints, 'sm-and-down')} {
        width: calc(100% - 35px);
      }

      @media only screen and (max-width: $xxs-and-down) {
        width: 100%;
        left: 0;
      }
    }

    &:first-child {
      font-size: 14px;
      font-weight: 500;
    }

    &:first-child,
    &:last-child {
      &::before {
        display: none;
      }
    }
  }

  .item {
    text-align: center;

    &:first-child {
      width: 35px;
      font-size: 14px;
      font-weight: 500;
    }

    &:not(:first-child) {
      width: 84px;
    }
  }

  .item_text {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 20px;
    font-size: 14px;
    min-width: 196px;
    font-weight: 400;

    @media (max-width: 768px) {
      justify-content: center;
      padding-left: 0;
      padding-top: 8px;
    }
  }
}
</style>

<style lang="scss">
.price-input {
  .v-input__slot {
    padding: 0 10px 0 8px !important;
  }

  .v-text-field__prefix {
    letter-spacing: -0.2px;
  }
}
</style>
