<template>
  <l-dialog
    :dialog="showCheckEmailDialog"
    hide-close-button
    max-width="418"
    custom-class="check-email"
  >
    <div>
      <div class="check-email-title">
        {{ $t('almost_there') }}
      </div>
      <div class="check-email-text mt-3">
        {{
          $t(
            'please_check_your_email_to_verify_your_email_address_and_create_password'
          )
        }}
      </div>
      <div class="check-email-button mt-4">
        <v-btn
          color="orange"
          class="font-weight-medium"
          min-width="178"
          x-large
          @click="closeCheckEmailDialog"
        >
          OK
        </v-btn>
      </div>
    </div>
  </l-dialog>
</template>

<script>
import LDialog from '~/components/LDialog'

export default {
  name: 'CheckEmailDialog',
  components: { LDialog },
  props: {
    showCheckEmailDialog: {
      type: Boolean,
      required: true,
    },
  },
  methods: {
    closeCheckEmailDialog() {
      this.$emit('close')
      this.$router.replace({ path: '/teachers/1' })
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/vars';

.check-email {
  &-title {
    color: var(--v-dark-base) !important;
    font-size: 20px;
    font-weight: 600;

    @media only screen and (max-width: $xsm-and-down) {
      text-align: center;
      font-size: 18px;
    }
  }

  &-button {
    text-align: right;

    @media only screen and (max-width: $xsm-and-down) {
      text-align: center;
    }

    .v-btn {
      border-radius: 16px !important;
    }
  }
}
</style>
