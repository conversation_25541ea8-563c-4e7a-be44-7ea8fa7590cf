/**
 * Helper functions for proficiency level translations
 */

/**
 * Translate proficiency level name based on current locale
 * @param {string} levelName - The proficiency level name (e.g., "A1", "B2")
 * @param {Function} $t - Vue i18n translation function
 * @returns {string} Translated proficiency level name
 */
export function translateProficiencyLevel(levelName, $t) {
  if (!levelName || !$t || typeof $t !== 'function') return levelName

  // Check if the level name is a direct translation key (A1, A2, etc.)
  const translationKey = levelName.trim()

  // Try to get translation, fallback to original name if not found
  try {
    const translation = $t(translationKey)

    // If translation returns the same key, it means no translation exists
    // Return the original name in that case
    return translation !== translationKey ? translation : levelName
  } catch (error) {
    // If there's an error with translation, return the original name
    // Silently handle translation errors to avoid console warnings
    return levelName
  }
}

/**
 * Format proficiency level with translation
 * @param {string} levelName - The proficiency level name
 * @param {Function} $t - Vue i18n translation function
 * @returns {string} Formatted proficiency level (e.g., "A1: Principiante")
 */
export function formatProficiencyLevel(levelName, $t) {
  if (!levelName || !$t || typeof $t !== 'function') return levelName

  const translated = translateProficiencyLevel(levelName, $t)

  // If we got a translation different from the original, format it
  if (translated !== levelName) {
    return `${levelName}: ${translated}`
  }

  // Otherwise return the original name
  return levelName
}
