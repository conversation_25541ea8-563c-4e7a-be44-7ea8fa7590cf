// Helper function to process all URL parameters and set up filters
export function processUrlParameters(query, filters, store) {
  if (!query || !filters) return

  // Process sortOption from query
  if (query.sortOption) {
    // Use try-catch to handle SSR cases where getters might not be available
    let sortByItem = null
    try {
      const sortByItems = store.getters['teacher_filter/sortByItems']
      if (sortByItems && Array.isArray(sortByItems)) {
        sortByItem = sortByItems.find(
          (item) => item.id === parseInt(query.sortOption)
        )
      }
    } catch (error) {
      console.warn(
        '[SSR] sortByItems getter not available during SSR:',
        error.message
      )
    }

    if (sortByItem && !sortByItem.isFeedbackTag) {
      store.commit('teacher_filter/SET_SELECTED_SORTING', sortByItem)
      store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
        data: sortByItem,
        type: 'sortOption',
      })
    }
  }

  // Process currency from query
  if (query.currency) {
    const currency = filters?.currencies?.find(
      (item) => item.id === parseInt(query.currency)
    )
    if (currency) {
      store.dispatch('currency/setItem', {
        item: currency,
        isCookieUpdate: false,
        setByUser: true,
      })
      store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
        data: currency,
        type: 'currency',
      })
    }
  }

  // Process motivation filter from query
  if (query.motivation) {
    const motivationId = parseInt(query.motivation)
    const motivation = filters?.motivations?.find((m) => m.id === motivationId)
    if (motivation) {
      store.commit('teacher_filter/SET_SELECTED_MOTIVATION', {
        motivation,
        updateActiveFilters: true,
      })
    }
  }

  // Process speciality filter from query
  if (query.speciality) {
    const specialityIds = query.speciality.split(',').map((id) => parseInt(id))
    const allSpecialities =
      filters?.motivations?.reduce((acc, motivation) => {
        if (motivation.specialities) {
          acc.push(...motivation.specialities)
        }
        return acc
      }, []) || []
    const specialities = allSpecialities.filter((s) =>
      specialityIds.includes(s.id)
    )
    if (specialities.length) {
      store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
        specialities,
        updateActiveFilters: true,
      })
      // Save specialty to cookie for persistence
      try {
        store.dispatch('teacher_filter/saveSpecialtyCookie')
      } catch (error) {
        console.warn(
          '[SSR] saveSpecialtyCookie not available during SSR:',
          error.message
        )
      }
    }
  }

  // Process language filter from query
  if (query.language) {
    const languageId = parseInt(query.language)
    const language = filters?.languages?.find((lang) => lang.id === languageId)
    if (language) {
      store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
        language,
        updateActiveFilters: true,
      })
    }
  }

  // Process teacher preference from query
  if (query.teacherPreference) {
    const teacherPrefId = parseInt(query.teacherPreference)
    const teacherPreferences = [
      { id: 0, name: 'A fantastic teacher from anywhere' },
      { id: 1, name: 'Native speaker' },
      { id: 2, name: 'Native speaker of a language I want to learn' },
      { id: 3, name: 'A teacher who also speaks' },
    ]
    const teacherPref = teacherPreferences.find((tp) => tp.id === teacherPrefId)
    if (teacherPref) {
      store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
        teacherPreference: teacherPref,
        updateActiveFilters: true,
      })
    }
  }

  // Process match languages from query
  if (query.matchLanguages) {
    const languageId = parseInt(query.matchLanguages)
    const selectedLanguage = filters?.languages?.find(
      (lang) => lang.id === languageId
    )
    if (selectedLanguage) {
      store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE', {
        teacherPreferenceLanguage: selectedLanguage,
        updateActiveFilters: true,
      })

      // Also set teacher preference to option 2 if it's not already set
      if (!query.teacherPreference) {
        const teacherPref = {
          id: 2,
          name: 'Native speaker of a language I want to learn',
        }
        store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
          teacherPreference: teacherPref,
          updateActiveFilters: true,
        })
      }
    }
  }

  // Process proficiency levels from query
  if (query.proficiencyLevels) {
    const proficiencyLevelIds = query.proficiencyLevels
      .split(',')
      .map((id) => parseInt(id))
    const proficiencyLevel = filters?.proficiencyLevels?.find((item) =>
      proficiencyLevelIds.includes(item.id)
    )
    if (proficiencyLevel) {
      store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
        proficiencyLevel,
        updateActiveFilters: true,
      })
    }
  }

  // Process dates from query
  if (query.dates) {
    const dateIds = query.dates.split(',').map((id) => parseInt(id))
    // Use try-catch to handle SSR cases where state might not be available
    try {
      const days = store.state.teacher_filter.days
      if (days && Array.isArray(days)) {
        const dates = days.filter((item) => dateIds.includes(item.id))
        if (dates.length) {
          store.commit('teacher_filter/SET_SELECTED_DAYS', {
            dates,
            updateActiveFilters: true,
          })
        }
      }
    } catch (error) {
      console.warn(
        '[SSR] teacher_filter.days not available during SSR:',
        error.message
      )
    }
  }

  // Process time from query
  if (query.time) {
    const timeIds = query.time.split(',').map((id) => parseInt(id))
    // Use try-catch to handle SSR cases where state might not be available
    try {
      const times = store.state.teacher_filter.times
      if (times && Array.isArray(times)) {
        const filteredTimes = times.filter((item) => timeIds.includes(item.id))
        if (filteredTimes.length) {
          store.commit('teacher_filter/SET_SELECTED_TIMES', {
            times: filteredTimes,
            updateActiveFilters: true,
          })
        }
      }
    } catch (error) {
      console.warn(
        '[SSR] teacher_filter.times not available during SSR:',
        error.message
      )
    }
  }

  // Process feedback tag from query
  if (query.tag) {
    const tagId = parseInt(query.tag)
    // Use try-catch to handle SSR cases where getters might not be available
    let feedbackTag = null
    try {
      const feedbackTags = store.getters['teacher_filter/feedbackTags']
      if (feedbackTags && Array.isArray(feedbackTags)) {
        feedbackTag = feedbackTags.find((item) => item.id === tagId)
      }
    } catch (error) {
      console.warn(
        '[SSR] feedbackTags getter not available during SSR:',
        error.message
      )
    }

    if (feedbackTag) {
      store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', feedbackTag)
      store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
        data: feedbackTag,
        type: 'tag',
      })
    }
  }

  // Process search query
  if (query.search) {
    store.commit('teacher_filter/SET_SEARCH_QUERY', {
      searchQuery: query.search,
      updateActiveFilters: true,
    })
  }
}
