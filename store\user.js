import dayjs from 'dayjs'

const defaultNewUser = () => ({
  userType: 1,
  username: null,
  firstName: null,
  lastName: null,
  email: null,
  currency: null,
  timezone: null,
  taxCountry: null,
  languageToLearn: null,
  uiLanguage: null,
  locale: null,
})

export const state = () => ({
  item: null,
  tidioData: null,
  newUserItem: defaultNewUser(),
  registrationPageItem: null,
  redirectUrl: null,
})

export const mutations = {
  SET_USER(state, payload) {
    state.item = payload
  },
  UPDATE_USER(state, payload) {
    state.item = Object.assign({}, state.item, payload)
  },
  UPDATE_NEW_USER_ITEM(state, payload) {
    state.newUserItem = Object.assign({}, state.newUserItem, payload)
  },
  RESET_NEW_USER_ITEM(state) {
    state.newUserItem = defaultNewUser()
  },
  SET_TIDIO_DATA(state, payload) {
    state.tidioData = payload
  },
  SET_REGISTRATION_PAGE_ITEM(state, payload) {
    state.registrationPageItem = payload
  },
  INCREASE_COUNT_UNREAD_MESSAGES(state, threadId) {
    if (!state.item.unreadThreadIds.includes(threadId)) {
      state.item.unreadThreadIds.push(threadId)
    }
  },
  DECREASE_COUNT_UNREAD_MESSAGES(state, threadId) {
    state.item.unreadThreadIds = state.item.unreadThreadIds.filter(
      (item) => item !== threadId
    )
  },
  DECREASE_UNSCHEDULED_LESSONS_NUMBER(state) {
    state.item.totalNumberUnscheduledLessons -= 1
  },
  SET_REDIRECT_URL(state, url) {
    state.redirectUrl = url
  },
  CLEAR_REDIRECT_URL(state) {
    state.redirectUrl = null
  },
}

export const getters = {
  isUserLogged: (state) => !!state.item?.id,
  isStudent: (state, getters) => {
    if (!getters.isUserLogged) return undefined

    return state.item?.userType === 'student'
  },
  isTeacher: (state, getters) => {
    if (!getters.isUserLogged) return undefined

    return state.item?.userType === 'teacher'
  },
  registrationConfirmed: (state, getters) => {
    if (!getters.isUserLogged) return false

    return state.item?.confirmedRegistration
  },
  teacherSlug: (state, getters) => {
    if (!getters.isUserLogged && getters.isStudent) return undefined

    return state.item?.username
  },
  newMessages: (state) => state.item?.unreadThreadIds?.length ?? 0,
  currency: (state) => state.item?.currency,
  language: (state) => state.item?.languageToLearn,
  timeZone: (state) =>
    state.item?.id ? state.item.timezone : dayjs.tz.guess(),
  totalNumberUnscheduledLessons: (state) =>
    state.item?.totalNumberUnscheduledLessons ?? 0,
  userCredit: (state) => state.item?.userCredit ?? null,
  lessonIdForFeedback: (state) => state.item?.lessonIdForFeedback ?? [],
  userTag: (state) => state.item?.userTag ?? null,
  getUserId: (state) => {
    return state?.item?.id ?? '0'
  },
  redirectUrl: (state) => state.redirectUrl,
}

export const actions = {
  getUserStatus({ commit, rootGetters, dispatch }) {
    const url = `${process.env.NUXT_ENV_API_URL}/user/get-logged-user`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then(async (data) => {
        if (!data) {
          this.$cookiz.remove('L2SESSID')
        } else {
          // const L2SESSID = this.$cookiz.get('L2SESSID')
          const userCurrencyId = data?.currency?.id
          const lang = data.uiLanguage

          if (this.$i18n && lang !== this.$i18n.locale) {
            this.$i18n.locale = lang

            this.$i18n.setLocaleCookie(lang)

            if (!this.$i18n.loadedLanguages.includes(lang)) {
              await import(`@/lang/${lang}.js`).then((messages) => {
                this.$i18n.setLocaleMessage(lang, messages.default)
              })
            }
          }

          if (userCurrencyId) {
            const currencies = rootGetters['currency/items']

            commit(
              'SET_SYSTEM_CURRENCY',
              currencies.find((item) => item.id === userCurrencyId),
              { root: true }
            )
          }

          // commit('SET_AUTH_COOKIE', L2SESSID, { root: true })
          commit('SET_IS_LOGIN_SIDEBAR', false, { root: true })
          commit('SET_LOCALE', data.uiLanguage, { root: true })

          await dispatch(
            'currency/setItem',
            { item: data.currency },
            { root: true }
          )
        }

        commit('SET_USER', data)

        await dispatch('user/getUserTidioData', null, { root: true })

        return data
      })
  },
  getUserTidioData({ commit, dispatch }) {
    dispatch('loadingAllow', false, { root: true })

    return (
      this.$axios
        .get(`${process.env.NUXT_ENV_API_URL}/users/tidio/data`)
        .then((response) => JSON.parse(response.data))
        .then((data) => {
          commit('SET_TIDIO_DATA', data)
        })
        // eslint-disable-next-line no-console
        .catch((e) => console.log(e))
        .finally(() => {
          dispatch('loadingAllow', true, { root: true })
        })
    )
  },
  getRegistrationPageItem({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/registration`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_REGISTRATION_PAGE_ITEM', data)
      })
  },
  checkEmail({ dispatch }, email) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/registration-check-email`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .post(
        url,
        JSON.stringify({
          email,
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          progress: false,
        }
      )
      .then((data) => data.data)
      .finally(() => {
        dispatch('loadingAllow', true, { root: true })
      })
  },
  checkUsername({ dispatch }, username) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/registration-check-username`

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .post(
        url,
        JSON.stringify({
          username,
        }),
        {
          headers: {
            'Content-Type': 'application/json',
          },
          progress: false,
        }
      )
      .then((data) => data.data)
      .finally(() => {
        dispatch('loadingAllow', true, { root: true })
      })
  },
  registration({ rootState, commit }, data) {
    const url = `${process.env.NUXT_ENV_API_URL}/users/registration`
    const utmTags = localStorage.getItem('utm')
    const preparedUtmData = {}
    if (utmTags) {
      const utmData = JSON.parse(utmTags)
      const utmMapping = {
        utm_source: 'utmSource',
        utm_campaign: 'utmCampaign',
        utm_ad: 'utmAd',
        utm_medium: 'utmMedium',
        utm_term: 'utmTerm',
        utm_content: 'utmContent',
        latest_utm_source: 'latestUtmSource',
      }
      Object.keys(utmData).forEach((key) => {
        preparedUtmData[utmMapping[key]] = utmData[key]
      })
    }
    let _data = data

    if (preparedUtmData) {
      _data = { ...data, ...preparedUtmData }
    }

    return this.$axios.post(url, _data).then(() => {
      // commit('SET_UTM_TAGS', null)
      // window.localStorage.removeItem('utm')
    })
  },
  refreshStatusOnline({ dispatch }, userIds) {
    const url = `${process.env.NUXT_ENV_API_URL}/user/statusonline`
    const formData = new FormData()

    userIds.forEach((id) => {
      formData.append('users[]', id)
    })

    dispatch('loadingAllow', false, { root: true })

    return this.$axios
      .post(url, formData, { progress: false })
      .then((response) => response.data)
      .then((data) => (Array.isArray(data) ? {} : data))
      .finally(() => dispatch('loadingAllow', true, { root: true }))
  },
  increaseCountUnreadMessages({ commit }, threadId) {
    commit('INCREASE_COUNT_UNREAD_MESSAGES', threadId)
  },
  decreaseCountUnreadMessages({ commit }, threadId) {
    commit('DECREASE_COUNT_UNREAD_MESSAGES', threadId)
  },
  updateLessonIdForFeedback({ state, commit }, lessonId) {
    commit('UPDATE_USER', {
      lessonIdForFeedback: state.item.lessonIdForFeedback.filter(
        (id) => id !== lessonId
      ),
    })
  },
  setRedirectUrl({ commit }, url) {
    commit('SET_REDIRECT_URL', url)
    // If url is already a path (not a full URL), use it directly
    let pathAndQuery = url
    if (url.startsWith('http')) {
      pathAndQuery = new URL(url).pathname + new URL(url).search
    }
    if (process.client) {
      window.localStorage.setItem('redirectUrl', JSON.stringify(pathAndQuery))
    }
  },
  loadRedirectUrl({ commit }) {
    if (process.client) {
      const redirectUrl = JSON.parse(window.localStorage.getItem('redirectUrl'))
      if (redirectUrl) {
        commit('SET_REDIRECT_URL', redirectUrl)
      }
    }
  },
  clearRedirectUrl({ commit }) {
    commit('CLEAR_REDIRECT_URL')
    if (process.client) {
      window.localStorage.removeItem('redirectUrl')
    }
  },
}
