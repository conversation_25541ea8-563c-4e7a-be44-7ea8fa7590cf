<!-- eslint-disable vue/no-v-html -->
<template>
  <v-col class="col-12 px-0">
    <div class="user-register">
      <v-form ref="form" v-model="valid" @submit.prevent="submit">
        <v-container fluid class="pa-0">
          <v-row no-gutters>
            <v-col class="col-12">
              <div class="user-register-wrap">
                <div class="user-register-title mb-3 mb-md-5">
                  {{ $t('welcome_to_langu') }} 👋
                </div>
                <div class="user-register-content">
                  <v-row>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('are_you_student_or_teacher') }}
                      </div>
                      <div>
                        <v-radio-group
                          :value="userType"
                          hide-details
                          class="mt-0 pt-0 mb-1"
                          @change="updateValue($event, 'userType')"
                        >
                          <div class="d-flex flex-wrap mb-2 mb-md-4">
                            <div
                              v-for="(ut, idx) in pageItem.userTypes"
                              :key="idx"
                              :class="['radiobutton', { 'mr-5': idx === 0 }]"
                            >
                              <v-radio
                                class="d-flex align-center l-radio-button"
                                color="success"
                                :ripple="false"
                                :value="ut.type"
                              >
                                <template #label>
                                  {{ getUserTypeLabels(ut.typeName) }}
                                </template>
                              </v-radio>
                            </div>
                          </div>
                        </v-radio-group>
                      </div>
                    </v-col>
                  </v-row>
                  <v-row v-if="userType === 2">
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('choose_your_username') }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1 order-2 order-sm-1">
                      <div class="input-wrap-label">
                        {{ $t('this_will_be_visible_in_link_to_your_profile') }}
                      </div>
                      <text-input
                        :value="item.username"
                        type-class="border-gradient"
                        height="44"
                        :rules="[
                          rules.usernameLength,
                          rules.usernameChar,
                          rules.isUsernameExist,
                        ]"
                        @input="updateUsername"
                      ></text-input>
                    </v-col>
                    <v-col
                      class="col-12 col-sm-6 d-flex align-end mb-1 mb-sm-3 order-1 order-sm-2"
                    >
                      <div
                        v-if="!item.username"
                        class="alert-wrap d-flex align-center pb-sm-3"
                      >
                        <v-alert class="mb-0" text type="error">
                          <template #prepend>
                            <div class="d-flex align-center mr-1">
                              <svg width="18" height="18" viewBox="0 0 12 12">
                                <use
                                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#attention`"
                                ></use>
                              </svg>
                            </div>
                          </template>
                          {{ $t('this_value_should_not_be_left_blank') }}
                        </v-alert>
                      </div>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('your_name') }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-sm-1">
                      <text-input
                        :value="item.firstName"
                        type-class="border-gradient"
                        height="44"
                        :rules="[rules.required]"
                        :placeholder="$t('first_name')"
                        autocomplete="given-name"
                        @input="updateValue($event, 'firstName')"
                      ></text-input>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1">
                      <text-input
                        :value="item.lastName"
                        type-class="border-gradient"
                        height="44"
                        :rules="[rules.required]"
                        :placeholder="$t('last_name')"
                        autocomplete="family-name"
                        @input="updateValue($event, 'lastName')"
                      ></text-input>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('email_address') }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1">
                      <text-input
                        :value="item.email"
                        type-class="border-gradient"
                        height="44"
                        :rules="[
                          rules.required,
                          rules.email,
                          rules.isEmailExist,
                        ]"
                        :disabled="registrationByGoogle"
                        :placeholder="$t('enter_your_email')"
                        autocomplete="email"
                        @input="updateEmail"
                      ></text-input>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('your_currency') }}
                      </div>
                      <div>
                        <v-radio-group
                          :value="item.currency"
                          hide-details
                          class="mt-0 pt-0"
                          @change="updateValue($event, 'currency')"
                        >
                          <div class="d-flex flex-wrap mb-3 mb-md-4">
                            <div
                              v-for="(c, idx) in currencies"
                              :key="idx"
                              class="radiobutton mb-1 mr-3"
                            >
                              <v-radio
                                class="d-flex align-center l-radio-button"
                                color="success"
                                :ripple="false"
                                :value="c.isoCode"
                              >
                                <template #label>
                                  {{ c.isoCode }}
                                </template>
                              </v-radio>
                            </div>
                          </div>
                        </v-radio-group>
                      </div>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('your_time_zone') }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6">
                      <div class="input-wrap-label">
                        {{
                          $t(
                            'select_your_current_local_time_dates_and_times_throughout_this_site_will_be_displayed_using_this_time_zone'
                          )
                        }}
                      </div>
                      <user-setting-autocomplete
                        :value="selectedTimeZone"
                        :items="pageItem.timezones"
                        item-text="gmt"
                        attach-id="time-zone"
                        :hide-details="false"
                        placeholder="choose_time_zone"
                        @change="updateValue($event.name, 'timezone')"
                      ></user-setting-autocomplete>
                    </v-col>
                    <v-col v-if="prepareTimeZones" class="col-12 mb-2 mb-md-3">
                      <div class="time-zone-list d-flex flex-wrap">
                        <div
                          v-for="ptz in prepareTimeZones"
                          :key="ptz.id"
                          :class="[
                            'item unselected mr-1 mr-md-2 mb-1 mb-md-2',
                            { selected: item.timezone === ptz.name },
                          ]"
                          @click.stop.prevent="
                            updateValue(ptz.name, 'timezone')
                          "
                        >
                          <div class="d-flex align-center body-2">
                            <span class="font-weight-medium">{{ ptz.tz }}</span
                            >&nbsp;
                            {{ ptz.name }}
                          </div>
                        </div>
                      </div>
                    </v-col>
                  </v-row>
                  <v-row v-if="userType === 2">
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('in_which_country_do_you_normally_pay_taxes') }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1 mb-md-3">
                      <user-setting-select
                        :value="selectedTaxCountry"
                        :items="pageItem.taxCountries"
                        attach-id="country-pay-taxes"
                        :hide-details="false"
                        :rules="[rules.selectRequired]"
                        validate-on-blur
                        placeholder="choose_country"
                        @change="updateValue($event.id, 'taxCountry')"
                      ></user-setting-select>
                    </v-col>
                  </v-row>
                  <v-row v-else>
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{
                          $t('what_language_would_you_like_to_learn_on_langu')
                        }}
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1">
                      <user-setting-select
                        :value="selectedLanguageToLearn"
                        :items="languagesToLearn"
                        attach-id="learn-language"
                        :hide-details="false"
                        :placeholder="
                          !selectedLanguageToLearn ? 'all_languages' : ''
                        "
                        :max-height="250"
                        @change="updateValue($event.id, 'languageToLearn')"
                      ></user-setting-select>
                    </v-col>
                  </v-row>
                  <v-row v-if="userType === 1">
                    <v-col class="col-12">
                      <div
                        class="input-wrap-title body-1 font-weight-medium mb-1"
                      >
                        {{ $t('how_did_you_find_out_about_langu') }}
                        <span style="font-size: 14px"
                          >({{ $t('optional') }})</span
                        >
                      </div>
                    </v-col>
                    <v-col class="col-12 col-sm-6 mb-1 mb-md-3">
                      <user-setting-select
                        :value="selectedReferralSource"
                        :items="referralSources"
                        attach-id="referral-source"
                        :hide-details="false"
                        placeholder="choose_referral_source"
                        :max-height="250"
                        @change="updateValue($event.id, 'referralSource')"
                      ></user-setting-select>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>
                      <v-checkbox
                        :value="isAgree"
                        class="l-checkbox caption"
                        :ripple="false"
                        :rules="[rules.agree]"
                        @change="isAgree = $event"
                      >
                        <template #label>
                          <span
                            class="checkbox-label body-2"
                            v-html="
                              $t('i_agree_to_role_terms_and_conditions', {
                                role,
                                link: termsLink,
                              })
                            "
                          >
                          </span>
                        </template>
                      </v-checkbox>
                    </v-col>
                  </v-row>
                </div>
                <div class="user-register-footer">
                  <v-row>
                    <v-col class="col-12">
                      <div class="d-flex justify-center justify-sm-end">
                        <v-btn
                          color="primary"
                          class="font-weight-medium"
                          type="submit"
                          :disabled="!valid"
                        >
                          {{ $t('submit') }}
                          <svg
                            class="ml-1"
                            width="18"
                            height="18"
                            viewBox="0 0 18 18"
                          >
                            <use
                              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#save-icon`"
                            ></use>
                          </svg>
                        </v-btn>
                      </div>
                    </v-col>
                  </v-row>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-form>
    </div>
  </v-col>
</template>

<script>
import { debounce } from '@/helpers'

import TextInput from '@/components/form/TextInput'
import UserSettingSelect from '@/components/user-settings/UserSettingSelect'
import UserSettingAutocomplete from '@/components/user-settings/UserSettingAutocomplete'

// Import the navigation state helper
import { applyNavigationState } from '@/helpers/navigationState'

const isEmailValid = (v) => /.+@.+\..+/.test(v)
const isUsernameLengthValid = (v) => !!v && v.length >= 5 && v.length <= 40
const isUsernameCharValid = (v) => !!v && /^[a-zA-Z0-9.-]*$/.test(v)

export default {
  name: 'UserRegisterPage',
  components: {
    TextInput,
    UserSettingSelect,
    UserSettingAutocomplete,
  },
  middleware({ store, redirect }) {
    if (store.getters['user/isUserLogged']) {
      return redirect('/')
    }
  },
  async asyncData({ store }) {
    await store.dispatch('user/getRegistrationPageItem')
  },
  data() {
    return {
      termsLinkEl: null,
      valid: true,
      isAgree: false,
      isEmailExist: false,
      isUsernameExist: false,
      languagesToLearn: [
        {
          id: 'all',
          name: this.$t('all_languages'),
        },
      ],
      referralSources: [
        { id: 'google search', name: this.$t('google_search') },
        { id: 'google ad', name: this.$t('google_ad') },
        { id: 'instagram', name: this.$t('instagram') },
        { id: 'facebook group', name: this.$t('facebook_group') },
        { id: 'facebook ad', name: this.$t('facebook_ad') },
        { id: 'olx', name: this.$t('olx') },
        { id: 'ebay kleinanzeigen', name: this.$t('ebay_kleinanzeigen') },
        {
          id: 'recommendation_from_teacher',
          name: this.$t('recommendation_from_teacher'),
        },
        {
          id: 'recommendation_from_friend_family',
          name: this.$t('recommendation_from_friend_family'),
        },
        { id: 'provided_by_employer', name: this.$t('provided_by_employer') },
        { id: 'chatgpt', name: this.$t('chatgpt') },
      ],
      prepareTimeZones: null,
      registrationByGoogle: false,
      rules: {
        usernameLength: (v) =>
          isUsernameLengthValid(v) || this.$t('username_length'),
        usernameChar: (v) =>
          isUsernameCharValid(v) || this.$t('username_is_invalid'),
        required: (v) =>
          (!!v && v.length > 0) ||
          this.$t('this_value_should_not_be_left_blank'),
        selectRequired: (v) =>
          !!v?.id || this.$t('this_value_should_not_be_left_blank'),
        agree: (v) => !!v || this.$t('please_check_box_if_you_want_to_proceed'),
        email: (v) => isEmailValid(v) || this.$t('email_must_be_valid'),
        isEmailExist: () =>
          !this.isEmailExist || this.$t('email_is_already_exist'),
        isUsernameExist: () =>
          !this.isUsernameExist || this.$t('username_is_already_exist'),
      },
    }
  },
  head() {
    return {
      title: this.$t('user_register_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('user_register_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('user_register_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('user_register_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} user-register-page`,
      },
    }
  },
  computed: {
    pageItem() {
      return this.$store.state.user.registrationPageItem
    },
    locale() {
      return this.$i18n.locale
    },
    currencies() {
      return this.pageItem.currencies.filter(
        (item) =>
          item[`isAvailableFor${this.userType === 1 ? 'Student' : 'Teacher'}`]
      )
    },
    currency() {
      return this.$store.state.currency.item
    },
    item() {
      return this.$store.state.user.newUserItem
    },
    userType() {
      return this.item.userType
    },
    selectedTimeZone() {
      return (
        this.pageItem?.timezones.find(
          (item) => item.name === this.item.timezone
        ) || {}
      )
    },
    selectedLanguageToLearn() {
      if (!this.item.languageToLearn) return null
      return (
        this.languagesToLearn.find(
          (item) => item.id === this.item.languageToLearn
        ) || null
      )
    },
    selectedTaxCountry() {
      return (
        this.pageItem?.taxCountries.find(
          (item) => item.id === this.item.taxCountry
        ) || {}
      )
    },
    termsLink() {
      return `/terms/${this.userType === 1 ? 'student' : 'teacher'}`
    },
    role() {
      return this.$t(this.userType === 1 ? 'student' : 'teacher').toLowerCase()
    },
    userTypes() {
      return this.pageItem.userType
    },
    selectedReferralSource() {
      return (
        this.referralSources.find(
          (item) => item.id === this.item.referralSource
        ) || {}
      )
    },
  },
  watch: {
    isUsernameExist() {
      this.validate()
    },
    isEmailExist() {
      this.validate()
    },
  },
  mounted() {
    const locale = this.$store.state.locale
    const userTimezone = this.$dayjs.tz.guess()

    this.languagesToLearn = [
      ...this.languagesToLearn,
      ...this.pageItem.languagesToLearn,
    ]

    this.prepareTimeZones = this.pageItem?.timezones
      .filter(
        (item) =>
          this.$dayjs().tz(item.name).utcOffset() === this.$dayjs().utcOffset()
      )
      .map((item) => ({ ...item, tz: item.gmt.split(' ')[0] }))

    const presetUserTimezone = this.prepareTimeZones.find(
      (item) => item.name === userTimezone
    )

    let presetData = {
      locale,
      uiLanguage: locale,
      currency: this.currency.isoCode,
      timezone: presetUserTimezone?.name ?? null,
    }

    if (this.pageItem?.googleData?.email) {
      this.registrationByGoogle = true
      presetData = {
        ...presetData,
        email: this.pageItem?.googleData?.email ?? null,
        firstName: this.pageItem?.googleData?.firstName ?? null,
        lastName: this.pageItem?.googleData?.lastName ?? null,
      }
    }

    this.$store.commit('user/UPDATE_NEW_USER_ITEM', presetData)

    this.$nextTick(() => {
      this.termsLinkEl = document.getElementById('terms-link')

      if (this.termsLinkEl) {
        this.termsLinkEl.addEventListener(
          'click',
          () => {
            window.open(this.termsLink, '_blank')
          },
          null
        )
      }
    })
  },
  beforeDestroy() {
    this.isEmailExist = false
    this.isUsernameExist = false

    this.$store.commit('user/RESET_NEW_USER_ITEM')
  },
  methods: {
    updateUsername(value) {
      if (isUsernameLengthValid(value) && isUsernameCharValid(value)) {
        this.checkUsername(value)
      }

      this.updateValue(value, 'username')
    },
    checkUsername: debounce(function (value) {
      this.$store.dispatch('user/checkUsername', value).then((data) => {
        this.isUsernameExist = !data.isValid
      })
    }, 800),
    updateEmail(value) {
      if (isEmailValid(value)) {
        this.checkEmail(value)
      }

      this.updateValue(value, 'email')
    },
    checkEmail: debounce(function (value) {
      this.$store.dispatch('user/checkEmail', value).then((data) => {
        this.isEmailExist = !data.isValid
      })
    }, 800),
    updateValue(value, property) {
      if (property === 'referralSource') {
        const selectedSource = this.referralSources.find(
          (source) => source.id === value
        )
        this.$store.commit('user/UPDATE_NEW_USER_ITEM', {
          [property]: selectedSource ? selectedSource.id : value,
        })
      } else {
        this.$store.commit('user/UPDATE_NEW_USER_ITEM', {
          [property]: value,
        })
      }
    },
    getUserTypeLabels(userType) {
      return userType.toLowerCase() === 'student'
        ? this.$t('student')
        : userType.toLowerCase() === 'teacher'
        ? this.$t('teacher_capitalize')
        : 'Default'
    },
    submit() {
      this.$store
        .dispatch('user/registration', this.item)
        .then(async () => {
          let path = '/teachers?checkEmail=1'
          const redirectUrl = this.$store.getters['user/redirectUrl']
          const isTeacherRegistration = this.userType === 2
          const isStudentRegistration = this.userType === 1

          if (this.registrationByGoogle) {
            await this.$store.dispatch('user/getUserStatus').then(() => {
              if (
                this.$store.getters['user/isStudent'] &&
                !this.$store.getters['user/registrationConfirmed']
              ) {
                path = '/teachers/welcome'
              } else {
                path = '/user/lessons'
              }
            })
          }

          // For teacher email registrations (not Google), always show the email verification modal
          if (isTeacherRegistration && !this.registrationByGoogle) {
            this.$router.push({ path: '/teachers?checkEmail=1' })
            return
          }

          // For student email registrations (not Google), show the email verification modal
          if (isStudentRegistration && !this.registrationByGoogle) {
            this.$router.push({ path: '/teachers?checkEmail=1' })
            return
          }

          // Try to apply the saved navigation state
          const navigationStateApplied = applyNavigationState(this.$router)
          // If navigation state was applied, we're done
          if (navigationStateApplied) {
            return
          }
          // Otherwise, use the redirectUrl or default path
          if (redirectUrl) {
            this.$router.push(redirectUrl)
            this.$store.dispatch('user/clearRedirectUrl')
          } else {
            this.$router.push({ path })
          }
        })
        .catch((e) => {
          let error = null

          if (e.response) {
            if (e.response.status === 403) {
              error = {
                errorMessage: 'username_is_already_exist',
              }
              this.isUsernameExist = true
            }

            if (e.response.status === 404) {
              error = {
                errorMessage: 'email_is_already_exist',
              }
              this.isEmailExist = true
            }

            this.validate()
          }

          this.$store.dispatch('snackbar/error', error)
          this.$vuetify.goTo(0, {
            duration: 300,
            offset: 0,
            easing: 'linear',
          })
        })
    },
    validate() {
      this.$refs.form.validate()
    },
  },
}
</script>

<style lang="scss">
@import './assets/styles/user-register.scss';

// Only apply light grey color when "All languages" is selected (when languageToLearn is null)
.user-setting-select[data-selected='all-languages'] {
  .v-select__selections {
    .v-select__selection--comma {
      color: var(--v-greyLight-base) !important;
    }
  }
}

// Fix timezone dropdown cursor and selection issues
.user-setting-autocomplete {
  .v-autocomplete {
    cursor: pointer !important;
  }
  .v-input__slot {
    cursor: pointer !important;
  }
  .v-select__slot {
    cursor: pointer !important;
  }
  // Prevent blue selection color
  .v-list-item--active {
    background-color: transparent !important;
    color: inherit !important;
  }
  .v-list-item:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
  }
  // Remove any auto-selection styling
  .v-list-item--highlighted {
    background-color: transparent !important;
  }
}
</style>
