export const state = () => ({
  items: [],
  totalQuantity: 0,
})

export const mutations = {
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
  SET_TOTAL_QUANTITY: (state, payload) => {
    state.totalQuantity = payload
  },
}

export const getters = {
  totalPages: (state) =>
    Math.ceil(state.totalQuantity / process.env.NUXT_ENV_PER_PAGE),
}

export const actions = {
  getHomePageTeachers({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-teachers`
    return (
      this.$axios
        .get(url)
        .then((response) => JSON.parse(response.data))
        .then((data) => {
          if (Array.isArray(data)) {
            commit('SET_ITEMS', data)

            return data
          }

          return []
        })
        // eslint-disable-next-line no-console
        .catch((e) => console.log(e))
    )
  },
  getTeachers({ commit, rootState }, { page, perPage, params, searchQuery }) {
    const url = `${process.env.NUXT_ENV_API_URL}/teachers`

    // Get URL parameters from store for new URL structure
    const urlParams = rootState.urlParams

    // Prepare headers for new URL structure
    const headers = {}
    if (urlParams.siteLanguage) {
      headers['X-Site-Language'] = urlParams.siteLanguage
    }
    if (urlParams.speciality) {
      headers['X-Teacher-Speciality'] = urlParams.speciality
    }
    if (urlParams.languageToLearn) {
      headers['X-Language-To-Learn'] = urlParams.languageToLearn
    }

    // Prepare query parameters - page and perPage are ALWAYS required
    const queryParams = {
      page: page || 1,
      perPage: perPage || 16,
    }

    if (params && params.length) {
      queryParams.searchString = params
    }

    if (searchQuery) {
      queryParams.searchString = queryParams.searchString
        ? `${queryParams.searchString};searchText,${searchQuery}`
        : `searchText,${searchQuery}`
    }

    return this.$axios
      .get(url, { headers, params: queryParams })
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        let teachers = data?.teachers ?? []
        const originalTotalCount = data?.countTeachers ?? 0

        // Client-side filtering when specialities are selected
        const selectedSpecialities =
          rootState.teacher_filter.selectedSpecialities
        if (selectedSpecialities && selectedSpecialities.length > 0) {
          teachers = teachers.filter((teacher) => {
            if (teacher.specialities && Array.isArray(teacher.specialities)) {
              const hasSpeciality = selectedSpecialities.some((selectedSpec) =>
                teacher.specialities.some(
                  (teacherSpecObj) =>
                    teacherSpecObj.speciality &&
                    teacherSpecObj.speciality.id === selectedSpec.id
                )
              )
              if (hasSpeciality) {
                const reorderedSpecialties = [...teacher.specialities]
                selectedSpecialities.forEach((selectedSpec) => {
                  const selectedIndex = reorderedSpecialties.findIndex(
                    (spec) =>
                      spec.speciality && spec.speciality.id === selectedSpec.id
                  )
                  if (selectedIndex > -1) {
                    const [selected] = reorderedSpecialties.splice(
                      selectedIndex,
                      1
                    )
                    reorderedSpecialties.unshift(selected)
                  }
                })
                teacher.specialities = reorderedSpecialties
              }
              return hasSpeciality
            }
            return false
          })
          commit('SET_ITEMS', teachers)
          commit('SET_TOTAL_QUANTITY', originalTotalCount)
        } else {
          commit('SET_ITEMS', teachers)
          commit('SET_TOTAL_QUANTITY', originalTotalCount)
        }
      })
  },
}
