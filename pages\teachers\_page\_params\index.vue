<template>
  <v-row>
    <teacher-listing
      :teachers="teachers"
      :params="params"
      :faq-items="faqItems"
      :page="page"
    ></teacher-listing>
  </v-row>
</template>

<script>
import TeacherListing from '~/components/teacher-listing/TeacherListing'
import { getSpecialityId, getLanguageId } from '~/helpers/urlParams'
import { processUrlParameters } from '~/helpers/processUrlParameters'

export default {
  name: 'TeacherListingFiltersPage',
  components: { TeacherListing },
  middleware: 'teacherListingRedirect',
  async asyncData({ params, store, query }) {
    const page = +params.page
    const currentCurrency = store.state.currency.item
    const searchQuery = query?.search
    const selectedSorting = store.state.teacher_filter.selectedSorting
    let paramsStr = params.params || ''

    let filters

    // Set flag to prevent navigation during initial load
    store.commit('teacher_filter/SET_LOADING_FROM_URL', true)

    // Only fetch filters if not already loaded
    if (!store.state.teacher_filter.filters) {
      await store
        .dispatch('teacher_filter/getFilters', { skipAutoSelect: true })
        .then((data) => (filters = data))
    } else {
      filters = store.state.teacher_filter.filters
    }

    // DON'T reset filters - they should persist based on URL params

    // Process all query parameters comprehensively
    if (query) {
      processUrlParameters(query, filters, store)

      // Build params string for API call
      const appliedKeys = new Set()
      Object.keys(query).forEach((key) => {
        if (key !== 'search' && query[key] && !appliedKeys.has(key)) {
          paramsStr += paramsStr
            ? `;${key},${query[key]}`
            : `${key},${query[key]}`
          appliedKeys.add(key)
        }
      })
    }

    // Check for URL parameters from new URL structure
    const urlParams = store.getters.urlParams

    if (urlParams.speciality && urlParams.languageToLearn) {
      // Get all specialities from all motivations
      const allSpecialities =
        filters?.motivations?.reduce((acc, motivation) => {
          if (motivation.specialities) {
            acc.push(...motivation.specialities)
          }
          return acc
        }, []) || []

      const specialityId = getSpecialityId(
        urlParams.speciality,
        allSpecialities
      )
      const languageId = getLanguageId(
        urlParams.languageToLearn,
        filters?.languages || []
      )

      if (specialityId && languageId) {
        // Set selected filters in store for UI display
        const selectedSpeciality = allSpecialities.find(
          (spec) => spec.id === specialityId
        )
        const selectedLanguage = filters.languages.find(
          (lang) => lang.id === languageId
        )

        if (selectedSpeciality) {
          store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
            specialities: [selectedSpeciality],
            updateActiveFilters: true, // Need for chips, but prevent navigation
          })
          // Save specialty to cookie for persistence
          store.dispatch('teacher_filter/saveSpecialtyCookie')
        }

        if (selectedLanguage) {
          store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language: selectedLanguage,
            updateActiveFilters: true, // Need for chips, but prevent navigation
          })
        }

        // Prepend speciality and language to params string
        paramsStr = `speciality,${specialityId};language,${languageId};${paramsStr}`
      }
    }

    if (!paramsStr.includes('sortOption')) {
      paramsStr += `;sortOption,${selectedSorting.id}`
    }
    await store.dispatch('teacher/getTeachers', {
      page,
      perPage: process.env.NUXT_ENV_PER_PAGE,
      params: paramsStr,
      searchQuery,
    })

    // Don't reset active filters here - they contain the chips that should be displayed
    // The filters were already set up correctly from URL parameters above
    store.commit('teacher_filter/SET_NEED_UPDATE_TEACHERS', false)

    const selectedTeacherPreference = filters.teacherPreference.find(
      (item) => item.id === 0
    )

    let specialities = []
    let selectedCurrency = filters.currencies.find(
      (item) => item.id === currentCurrency.id
    )

    if (selectedTeacherPreference) {
      store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
        teacherPreference: selectedTeacherPreference,
        updateActiveFilters: true,
      })
    }

    if (searchQuery) {
      store.commit('teacher_filter/SET_SEARCH_QUERY', {
        searchQuery,
        updateActiveFilters: true,
      })
    }

    paramsStr.split(';').forEach((item) => {
      let arr = item.split(',')

      const property = arr[0]

      arr.splice(0, 1)
      arr = arr.map((item) => +item)

      if (property === 'sortOption') {
        const sortByItem = store.getters[
          'teacher_filter/sortByItems'
        ]?.find((item) => arr.includes(item.id))

        if (sortByItem && !sortByItem.isFeedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_SORTING', sortByItem)
          store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
            data: sortByItem,
            type: 'sortOption',
          })
        }
      }

      if (property === 'language') {
        const language = filters?.languages.find((item) =>
          arr.includes(item.id)
        )

        if (language) {
          store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
            language,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'motivation') {
        const motivation = filters?.motivations.find((item) =>
          arr.includes(item.id)
        )

        if (motivation) {
          specialities = motivation.specialities

          store.commit('teacher_filter/SET_SELECTED_MOTIVATION', {
            motivation,
            updateActiveFilters: true,
          })
          store.commit('teacher_filter/SET_SPECIALITIES', specialities)
        }
      }

      if (property === 'speciality') {
        // Find all specialities from all motivations to ensure we can match regardless of URL parameter order
        const allSpecialities =
          filters?.motivations?.reduce((acc, motivation) => {
            if (motivation.specialities) {
              acc.push(...motivation.specialities)
            }
            return acc
          }, []) || []

        const selectedSpecialities = allSpecialities.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
          specialities: selectedSpecialities,
          updateActiveFilters: true,
        })
      }

      if (property === 'proficiencyLevels') {
        const proficiencyLevel = filters?.proficiencyLevels.find((item) =>
          arr.includes(item.id)
        )

        if (proficiencyLevel) {
          store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
            proficiencyLevel,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'teacherPreference') {
        const teacherPreference = filters?.teacherPreference.find((item) =>
          arr.includes(item.id)
        )

        if (teacherPreference) {
          store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
            teacherPreference,
            updateActiveFilters: true,
          })
        }
      }

      if (property === 'matchLanguages') {
        const teacherPreferenceLanguage = filters?.languages.find((item) =>
          arr.includes(item.id)
        )

        if (teacherPreferenceLanguage) {
          store.commit(
            'teacher_filter/SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE',
            { teacherPreferenceLanguage, updateActiveFilters: true }
          )
        }
      }

      if (property === 'dates') {
        const dates = store.state.teacher_filter.days.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_DAYS', {
          dates,
          updateActiveFilters: true,
        })
      }

      if (property === 'time') {
        const times = store.state.teacher_filter.times.filter((item) =>
          arr.includes(item.id)
        )

        store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times,
          updateActiveFilters: true,
        })
      }

      if (property === 'currency') {
        selectedCurrency = filters?.currencies.find((item) =>
          arr.includes(item.id)
        )
        if (selectedCurrency) {
          store.commit('currency/SET_CURRENCY', selectedCurrency)
          store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
            data: selectedCurrency,
            type: 'currency',
          })
        }
      }

      if (property === 'tag') {
        const feedbackTag = store.getters[
          'teacher_filter/feedbackTags'
        ].find((item) => arr.includes(item.id))

        if (feedbackTag) {
          store.commit('teacher_filter/SET_SELECTED_FEEDBACK_TAG', feedbackTag)
          store.commit('teacher_filter/UPDATE_ACTIVE_FILTERS', {
            data: feedbackTag,
            type: 'tag',
          })
        }
      }
    })

    if (selectedCurrency) {
      await store.dispatch('currency/setItem', { item: selectedCurrency })
      await store.dispatch('teacher_filter/updateCurrencyActiveFilter')
    }

    // DON'T clear the flag here - will be cleared in mounted()

    return { page, params: paramsStr }
  },
  head() {
    return {
      title: this.$t('teacher_listing_page.seo_title'),
      meta: [
        {
          hid: 'description',
          name: 'description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
        {
          hid: 'og:title',
          name: 'og:title',
          property: 'og:title',
          content: this.$t('teacher_listing_page.seo_title'),
        },
        {
          property: 'og:description',
          content: this.$t('teacher_listing_page.seo_description'),
        },
      ],
      bodyAttrs: {
        class: `${this.locale} teacher-listing-page`,
      },
    }
  },
  computed: {
    locale() {
      return this.$i18n.locale
    },
    teachers() {
      return this.$store.state.teacher.items
    },
    faqItems() {
      return this.$store.state.faq.teacherListItems
    },
  },
  watchQuery: true,
  mounted() {
    // Clear the loading flag after component is fully mounted
    this.$nextTick(() => {
      this.$store.commit('teacher_filter/SET_LOADING_FROM_URL', false)

      // Reprocess URL parameters on client side to ensure chips are displayed
      // This handles cases where SSR didn't fully process the parameters
      if (this.$route.query && Object.keys(this.$route.query).length > 0) {
        const {
          processUrlParameters,
        } = require('~/helpers/processUrlParameters')
        processUrlParameters(this.$route.query, this.filters, this.$store)
      }
    })
  },
  async beforeMount() {
    if (!this.faqItems.length) {
      await this.$store.dispatch('loadingAllow', false)
      this.$store
        .dispatch('faq/getTeacherListPageFaqs')
        .finally(() => this.$store.dispatch('loadingAllow', true))
    }
  },
}
</script>

<style lang="scss">
@import './assets/styles/teacher-listing.scss';
</style>
